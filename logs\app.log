2025-05-29 08:49:20,481 - root - INFO - 设备预定系统启动中...
2025-05-29 08:49:20,952 - root - INFO - FastAPI应用初始化中...
2025-05-29 08:49:20,955 - backend.i18n - INFO - 国际化设置成功
2025-05-29 08:49:21,651 - root - INFO - 应用启动 / Application started
2025-05-29 08:49:21,658 - backend.database - INFO - 数据库初始化成功
2025-05-29 08:49:21,658 - root - INFO - 后台任务已启动
2025-05-29 08:49:21,659 - root - INFO - 执行预约状态更新任务
2025-05-29 08:49:21,659 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 08:49:21.659816
2025-05-29 08:49:21,660 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=8, 分=49, 秒=21
2025-05-29 08:49:21,680 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 08:49:21,681 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 08:49:21.659816 AND Reservation.end_datetime > 2025-05-29 08:49:21.659816
2025-05-29 08:49:21,684 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 08:49:21,684 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 08:49:21,684 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 08:49:21.659816
2025-05-29 08:49:21,686 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 08:49:21,686 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 08:49:21,687 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 08:50:10,578 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 08:50:19,088 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 08:50:21,689 - root - INFO - 执行预约状态更新任务
2025-05-29 08:50:21,689 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 08:50:21.689711
2025-05-29 08:50:21,690 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=8, 分=50, 秒=21
2025-05-29 08:50:21,691 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 08:50:21,691 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 08:50:21.689711 AND Reservation.end_datetime > 2025-05-29 08:50:21.689711
2025-05-29 08:50:21,692 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 08:50:21,692 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 08:50:21,692 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 08:50:21.689711
2025-05-29 08:50:21,693 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 08:50:21,693 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 08:50:21,694 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 08:50:22,284 - backend.routes.reservation - INFO - 获取预约详情: 预约码=WM51NAWY, 开始时间=None, 结束时间=None, 预约序号=RN-20250623-9315-6
2025-05-29 08:50:22,285 - backend.routes.reservation - INFO - 提供了预约序号 RN-20250623-9315-6，优先使用预约序号查询
2025-05-29 08:50:22,285 - backend.routes.reservation - INFO - 使用预约序号查询: RN-20250623-9315-6
2025-05-29 08:50:22,286 - backend.routes.reservation - INFO - 通过预约序号直接找到预约: ID=233, 状态=cancelled
2025-05-29 08:50:22,287 - backend.routes.reservation - INFO - 通过预约序号查询成功，返回预约详情: ID=233, 状态=cancelled
2025-05-29 08:50:29,843 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 08:51:21,702 - root - INFO - 执行预约状态更新任务
2025-05-29 08:51:21,703 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 08:51:21.703300
2025-05-29 08:51:21,703 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=8, 分=51, 秒=21
2025-05-29 08:51:21,704 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 08:51:21,704 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 08:51:21.703300 AND Reservation.end_datetime > 2025-05-29 08:51:21.703300
2025-05-29 08:51:21,705 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 08:51:21,705 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 08:51:21,706 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 08:51:21.703300
2025-05-29 08:51:21,706 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 08:51:21,707 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 08:51:21,707 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 08:52:21,709 - root - INFO - 执行预约状态更新任务
2025-05-29 08:52:21,709 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 08:52:21.709595
2025-05-29 08:52:21,710 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=8, 分=52, 秒=21
2025-05-29 08:52:21,711 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 08:52:21,711 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 08:52:21.709595 AND Reservation.end_datetime > 2025-05-29 08:52:21.709595
2025-05-29 08:52:21,712 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 08:52:21,712 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 08:52:21,713 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 08:52:21.709595
2025-05-29 08:52:21,713 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 08:52:21,714 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 08:52:21,714 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 08:52:56,468 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 08:52:59,131 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 08:53:01,547 - backend.routes.reservation - INFO - 获取预约详情: 预约码=WM51NAWY, 开始时间=None, 结束时间=None, 预约序号=RN-20250623-9315-6
2025-05-29 08:53:01,547 - backend.routes.reservation - INFO - 提供了预约序号 RN-20250623-9315-6，优先使用预约序号查询
2025-05-29 08:53:01,548 - backend.routes.reservation - INFO - 使用预约序号查询: RN-20250623-9315-6
2025-05-29 08:53:01,549 - backend.routes.reservation - INFO - 通过预约序号直接找到预约: ID=233, 状态=cancelled
2025-05-29 08:53:01,550 - backend.routes.reservation - INFO - 通过预约序号查询成功，返回预约详情: ID=233, 状态=cancelled
2025-05-29 08:53:21,727 - root - INFO - 执行预约状态更新任务
2025-05-29 08:53:21,728 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 08:53:21.728218
2025-05-29 08:53:21,728 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=8, 分=53, 秒=21
2025-05-29 08:53:21,729 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 08:53:21,729 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 08:53:21.728218 AND Reservation.end_datetime > 2025-05-29 08:53:21.728218
2025-05-29 08:53:21,730 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 08:53:21,731 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 08:53:21,731 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 08:53:21.728218
2025-05-29 08:53:21,731 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 08:53:21,732 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 08:53:21,732 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 08:54:21,735 - root - INFO - 执行预约状态更新任务
2025-05-29 08:54:21,736 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 08:54:21.736090
2025-05-29 08:54:21,736 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=8, 分=54, 秒=21
2025-05-29 08:54:21,737 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 08:54:21,737 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 08:54:21.736090 AND Reservation.end_datetime > 2025-05-29 08:54:21.736090
2025-05-29 08:54:21,738 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 08:54:21,739 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 08:54:21,739 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 08:54:21.736090
2025-05-29 08:54:21,740 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 08:54:21,740 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 08:54:21,740 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 08:55:21,756 - root - INFO - 执行预约状态更新任务
2025-05-29 08:55:21,757 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 08:55:21.757309
2025-05-29 08:55:21,757 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=8, 分=55, 秒=21
2025-05-29 08:55:21,758 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 08:55:21,758 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 08:55:21.757309 AND Reservation.end_datetime > 2025-05-29 08:55:21.757309
2025-05-29 08:55:21,759 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 08:55:21,760 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 08:55:21,760 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 08:55:21.757309
2025-05-29 08:55:21,761 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 08:55:21,761 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 08:55:21,761 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 08:56:21,770 - root - INFO - 执行预约状态更新任务
2025-05-29 08:56:21,771 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 08:56:21.771396
2025-05-29 08:56:21,772 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=8, 分=56, 秒=21
2025-05-29 08:56:21,773 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 08:56:21,773 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 08:56:21.771396 AND Reservation.end_datetime > 2025-05-29 08:56:21.771396
2025-05-29 08:56:21,774 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 08:56:21,774 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 08:56:21,774 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 08:56:21.771396
2025-05-29 08:56:21,775 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 08:56:21,775 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 08:56:21,776 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 08:57:21,787 - root - INFO - 执行预约状态更新任务
2025-05-29 08:57:21,788 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 08:57:21.788490
2025-05-29 08:57:21,789 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=8, 分=57, 秒=21
2025-05-29 08:57:21,790 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 08:57:21,790 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 08:57:21.788490 AND Reservation.end_datetime > 2025-05-29 08:57:21.788490
2025-05-29 08:57:21,791 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 08:57:21,792 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 08:57:21,792 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 08:57:21.788490
2025-05-29 08:57:21,793 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 08:57:21,794 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 08:57:21,795 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 08:58:21,798 - root - INFO - 执行预约状态更新任务
2025-05-29 08:58:21,798 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 08:58:21.798776
2025-05-29 08:58:21,799 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=8, 分=58, 秒=21
2025-05-29 08:58:21,800 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 08:58:21,800 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 08:58:21.798776 AND Reservation.end_datetime > 2025-05-29 08:58:21.798776
2025-05-29 08:58:21,801 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 08:58:21,801 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 08:58:21,801 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 08:58:21.798776
2025-05-29 08:58:21,802 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 08:58:21,802 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 08:58:21,802 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 08:59:21,808 - root - INFO - 执行预约状态更新任务
2025-05-29 08:59:21,808 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 08:59:21.808842
2025-05-29 08:59:21,809 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=8, 分=59, 秒=21
2025-05-29 08:59:21,809 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 08:59:21,810 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 08:59:21.808842 AND Reservation.end_datetime > 2025-05-29 08:59:21.808842
2025-05-29 08:59:21,810 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 08:59:21,811 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 08:59:21,811 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 08:59:21.808842
2025-05-29 08:59:21,812 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 08:59:21,812 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 08:59:21,812 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:00:21,817 - root - INFO - 执行预约状态更新任务
2025-05-29 09:00:21,818 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:00:21.818007
2025-05-29 09:00:21,818 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=0, 秒=21
2025-05-29 09:00:21,819 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:00:21,819 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:00:21.818007 AND Reservation.end_datetime > 2025-05-29 09:00:21.818007
2025-05-29 09:00:21,820 - backend.utils.status_updater - INFO - 找到 1 个应该更新为'使用中'的预约
2025-05-29 09:00:21,820 - backend.utils.status_updater - INFO - 预约ID: 179, 开始时间: 2025-05-29 09:00:00, 结束时间: 2025-05-29 17:00:00, 当前状态: confirmed
2025-05-29 09:00:21,821 - backend.utils.status_updater - INFO - 预约ID: 179, 当前时间与开始时间的差值(秒): 21.818007, 结束时间与当前时间的差值(秒): 28778.181993
2025-05-29 09:00:21,821 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:00:21,821 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:00:21.818007
2025-05-29 09:00:21,822 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:00:21,824 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=1条, 已过期=0条
2025-05-29 09:00:21,826 - backend.utils.status_updater - INFO - 已更新预约状态: 1个更新为'使用中', 0个更新为'已过期'
2025-05-29 09:01:21,840 - root - INFO - 执行预约状态更新任务
2025-05-29 09:01:21,840 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:01:21.840848
2025-05-29 09:01:21,841 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=1, 秒=21
2025-05-29 09:01:21,842 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:01:21,842 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:01:21.840848 AND Reservation.end_datetime > 2025-05-29 09:01:21.840848
2025-05-29 09:01:21,843 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:01:21,843 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:01:21,843 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:01:21.840848
2025-05-29 09:01:21,844 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:01:21,844 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:01:21,844 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:02:21,860 - root - INFO - 执行预约状态更新任务
2025-05-29 09:02:21,861 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:02:21.861194
2025-05-29 09:02:21,862 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=2, 秒=21
2025-05-29 09:02:21,864 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:02:21,864 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:02:21.861194 AND Reservation.end_datetime > 2025-05-29 09:02:21.861194
2025-05-29 09:02:21,865 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:02:21,865 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:02:21,866 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:02:21.861194
2025-05-29 09:02:21,867 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:02:21,867 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:02:21,868 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:03:21,877 - root - INFO - 执行预约状态更新任务
2025-05-29 09:03:21,877 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:03:21.877743
2025-05-29 09:03:21,878 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=3, 秒=21
2025-05-29 09:03:21,879 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:03:21,879 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:03:21.877743 AND Reservation.end_datetime > 2025-05-29 09:03:21.877743
2025-05-29 09:03:21,880 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:03:21,880 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:03:21,880 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:03:21.877743
2025-05-29 09:03:21,881 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:03:21,882 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:03:21,882 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:04:21,893 - root - INFO - 执行预约状态更新任务
2025-05-29 09:04:21,893 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:04:21.893948
2025-05-29 09:04:21,894 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=4, 秒=21
2025-05-29 09:04:21,895 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:04:21,895 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:04:21.893948 AND Reservation.end_datetime > 2025-05-29 09:04:21.893948
2025-05-29 09:04:21,896 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:04:21,896 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:04:21,896 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:04:21.893948
2025-05-29 09:04:21,897 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:04:21,897 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:04:21,898 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:05:21,912 - root - INFO - 执行预约状态更新任务
2025-05-29 09:05:21,912 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:05:21.912800
2025-05-29 09:05:21,913 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=5, 秒=21
2025-05-29 09:05:21,914 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:05:21,914 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:05:21.912800 AND Reservation.end_datetime > 2025-05-29 09:05:21.912800
2025-05-29 09:05:21,915 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:05:21,915 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:05:21,915 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:05:21.912800
2025-05-29 09:05:21,916 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:05:21,916 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:05:21,917 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:06:21,930 - root - INFO - 执行预约状态更新任务
2025-05-29 09:06:21,930 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:06:21.930963
2025-05-29 09:06:21,931 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=6, 秒=21
2025-05-29 09:06:21,932 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:06:21,932 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:06:21.930963 AND Reservation.end_datetime > 2025-05-29 09:06:21.930963
2025-05-29 09:06:21,933 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:06:21,933 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:06:21,933 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:06:21.930963
2025-05-29 09:06:21,934 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:06:21,934 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:06:21,934 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:07:21,938 - root - INFO - 执行预约状态更新任务
2025-05-29 09:07:21,938 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:07:21.938586
2025-05-29 09:07:21,939 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=7, 秒=21
2025-05-29 09:07:21,939 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:07:21,940 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:07:21.938586 AND Reservation.end_datetime > 2025-05-29 09:07:21.938586
2025-05-29 09:07:21,941 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:07:21,941 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:07:21,941 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:07:21.938586
2025-05-29 09:07:21,942 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:07:21,942 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:07:21,942 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:08:21,950 - root - INFO - 执行预约状态更新任务
2025-05-29 09:08:21,950 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:08:21.950725
2025-05-29 09:08:21,951 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=8, 秒=21
2025-05-29 09:08:21,952 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:08:21,952 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:08:21.950725 AND Reservation.end_datetime > 2025-05-29 09:08:21.950725
2025-05-29 09:08:21,953 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:08:21,953 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:08:21,953 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:08:21.950725
2025-05-29 09:08:21,954 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:08:21,954 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:08:21,955 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:09:21,959 - root - INFO - 执行预约状态更新任务
2025-05-29 09:09:21,960 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:09:21.960416
2025-05-29 09:09:21,960 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=9, 秒=21
2025-05-29 09:09:21,961 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:09:21,962 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:09:21.960416 AND Reservation.end_datetime > 2025-05-29 09:09:21.960416
2025-05-29 09:09:21,963 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:09:21,963 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:09:21,963 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:09:21.960416
2025-05-29 09:09:21,964 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:09:21,964 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:09:21,964 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:10:21,973 - root - INFO - 执行预约状态更新任务
2025-05-29 09:10:21,973 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:10:21.973590
2025-05-29 09:10:21,974 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=10, 秒=21
2025-05-29 09:10:21,974 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:10:21,975 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:10:21.973590 AND Reservation.end_datetime > 2025-05-29 09:10:21.973590
2025-05-29 09:10:21,976 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:10:21,976 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:10:21,977 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:10:21.973590
2025-05-29 09:10:21,977 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:10:21,978 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:10:21,978 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:11:21,989 - root - INFO - 执行预约状态更新任务
2025-05-29 09:11:21,990 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:11:21.990209
2025-05-29 09:11:21,990 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=11, 秒=21
2025-05-29 09:11:21,991 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:11:21,991 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:11:21.990209 AND Reservation.end_datetime > 2025-05-29 09:11:21.990209
2025-05-29 09:11:21,992 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:11:21,992 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:11:21,993 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:11:21.990209
2025-05-29 09:11:21,993 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:11:21,994 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:11:21,994 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:12:22,010 - root - INFO - 执行预约状态更新任务
2025-05-29 09:12:22,010 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:12:22.010878
2025-05-29 09:12:22,011 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=12, 秒=22
2025-05-29 09:12:22,012 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:12:22,012 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:12:22.010878 AND Reservation.end_datetime > 2025-05-29 09:12:22.010878
2025-05-29 09:12:22,013 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:12:22,013 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:12:22,014 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:12:22.010878
2025-05-29 09:12:22,014 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:12:22,015 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:12:22,015 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:13:22,031 - root - INFO - 执行预约状态更新任务
2025-05-29 09:13:22,031 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:13:22.031545
2025-05-29 09:13:22,031 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=13, 秒=22
2025-05-29 09:13:22,032 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:13:22,033 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:13:22.031545 AND Reservation.end_datetime > 2025-05-29 09:13:22.031545
2025-05-29 09:13:22,033 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:13:22,034 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:13:22,034 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:13:22.031545
2025-05-29 09:13:22,035 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:13:22,035 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:13:22,035 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:14:22,046 - root - INFO - 执行预约状态更新任务
2025-05-29 09:14:22,046 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:14:22.046842
2025-05-29 09:14:22,047 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=14, 秒=22
2025-05-29 09:14:22,048 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:14:22,048 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:14:22.046842 AND Reservation.end_datetime > 2025-05-29 09:14:22.046842
2025-05-29 09:14:22,049 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:14:22,049 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:14:22,049 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:14:22.046842
2025-05-29 09:14:22,050 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:14:22,050 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:14:22,051 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:15:22,064 - root - INFO - 执行预约状态更新任务
2025-05-29 09:15:22,065 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:15:22.065504
2025-05-29 09:15:22,065 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=15, 秒=22
2025-05-29 09:15:22,066 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:15:22,067 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:15:22.065504 AND Reservation.end_datetime > 2025-05-29 09:15:22.065504
2025-05-29 09:15:22,068 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:15:22,068 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:15:22,068 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:15:22.065504
2025-05-29 09:15:22,069 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:15:22,069 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:15:22,069 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:16:22,083 - root - INFO - 执行预约状态更新任务
2025-05-29 09:16:22,083 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:16:22.083864
2025-05-29 09:16:22,084 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=16, 秒=22
2025-05-29 09:16:22,085 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:16:22,086 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:16:22.083864 AND Reservation.end_datetime > 2025-05-29 09:16:22.083864
2025-05-29 09:16:22,087 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:16:22,087 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:16:22,087 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:16:22.083864
2025-05-29 09:16:22,088 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:16:22,088 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:16:22,089 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:17:22,092 - root - INFO - 执行预约状态更新任务
2025-05-29 09:17:22,093 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:17:22.093364
2025-05-29 09:17:22,093 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=17, 秒=22
2025-05-29 09:17:22,094 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:17:22,095 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:17:22.093364 AND Reservation.end_datetime > 2025-05-29 09:17:22.093364
2025-05-29 09:17:22,095 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:17:22,096 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:17:22,096 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:17:22.093364
2025-05-29 09:17:22,097 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:17:22,098 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:17:22,098 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:18:22,103 - root - INFO - 执行预约状态更新任务
2025-05-29 09:18:22,104 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:18:22.104187
2025-05-29 09:18:22,104 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=18, 秒=22
2025-05-29 09:18:22,105 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:18:22,105 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:18:22.104187 AND Reservation.end_datetime > 2025-05-29 09:18:22.104187
2025-05-29 09:18:22,106 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:18:22,106 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:18:22,107 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:18:22.104187
2025-05-29 09:18:22,107 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:18:22,108 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:18:22,108 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:19:22,109 - root - INFO - 执行预约状态更新任务
2025-05-29 09:19:22,109 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:19:22.109525
2025-05-29 09:19:22,109 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=19, 秒=22
2025-05-29 09:19:22,110 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:19:22,111 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:19:22.109525 AND Reservation.end_datetime > 2025-05-29 09:19:22.109525
2025-05-29 09:19:22,111 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:19:22,112 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:19:22,112 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:19:22.109525
2025-05-29 09:19:22,113 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:19:22,113 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:19:22,113 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:20:22,114 - root - INFO - 执行预约状态更新任务
2025-05-29 09:20:22,114 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:20:22.114601
2025-05-29 09:20:22,115 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=20, 秒=22
2025-05-29 09:20:22,116 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:20:22,116 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:20:22.114601 AND Reservation.end_datetime > 2025-05-29 09:20:22.114601
2025-05-29 09:20:22,117 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:20:22,117 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:20:22,117 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:20:22.114601
2025-05-29 09:20:22,118 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:20:22,118 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:20:22,118 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:21:22,121 - root - INFO - 执行预约状态更新任务
2025-05-29 09:21:22,121 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:21:22.121656
2025-05-29 09:21:22,122 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=21, 秒=22
2025-05-29 09:21:22,122 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:21:22,123 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:21:22.121656 AND Reservation.end_datetime > 2025-05-29 09:21:22.121656
2025-05-29 09:21:22,124 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:21:22,124 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:21:22,124 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:21:22.121656
2025-05-29 09:21:22,125 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:21:22,125 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:21:22,125 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:22:22,136 - root - INFO - 执行预约状态更新任务
2025-05-29 09:22:22,137 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:22:22.137280
2025-05-29 09:22:22,137 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=22, 秒=22
2025-05-29 09:22:22,138 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:22:22,138 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:22:22.137280 AND Reservation.end_datetime > 2025-05-29 09:22:22.137280
2025-05-29 09:22:22,139 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:22:22,139 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:22:22,139 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:22:22.137280
2025-05-29 09:22:22,140 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:22:22,140 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:22:22,140 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:23:22,141 - root - INFO - 执行预约状态更新任务
2025-05-29 09:23:22,142 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:23:22.142201
2025-05-29 09:23:22,142 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=23, 秒=22
2025-05-29 09:23:22,143 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:23:22,143 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:23:22.142201 AND Reservation.end_datetime > 2025-05-29 09:23:22.142201
2025-05-29 09:23:22,144 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:23:22,144 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:23:22,145 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:23:22.142201
2025-05-29 09:23:22,145 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:23:22,146 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:23:22,146 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:24:22,147 - root - INFO - 执行预约状态更新任务
2025-05-29 09:24:22,147 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:24:22.147693
2025-05-29 09:24:22,148 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=24, 秒=22
2025-05-29 09:24:22,148 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:24:22,149 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:24:22.147693 AND Reservation.end_datetime > 2025-05-29 09:24:22.147693
2025-05-29 09:24:22,150 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:24:22,150 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:24:22,150 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:24:22.147693
2025-05-29 09:24:22,151 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:24:22,151 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:24:22,152 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:25:22,156 - root - INFO - 执行预约状态更新任务
2025-05-29 09:25:22,157 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:25:22.156987
2025-05-29 09:25:22,157 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=25, 秒=22
2025-05-29 09:25:22,158 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:25:22,158 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:25:22.156987 AND Reservation.end_datetime > 2025-05-29 09:25:22.156987
2025-05-29 09:25:22,159 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:25:22,160 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:25:22,160 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:25:22.156987
2025-05-29 09:25:22,161 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:25:22,161 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:25:22,161 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:26:22,174 - root - INFO - 执行预约状态更新任务
2025-05-29 09:26:22,175 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:26:22.175233
2025-05-29 09:26:22,175 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=26, 秒=22
2025-05-29 09:26:22,176 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:26:22,176 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:26:22.175233 AND Reservation.end_datetime > 2025-05-29 09:26:22.175233
2025-05-29 09:26:22,177 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:26:22,177 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:26:22,178 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:26:22.175233
2025-05-29 09:26:22,178 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:26:22,178 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:26:22,179 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:27:22,184 - root - INFO - 执行预约状态更新任务
2025-05-29 09:27:22,184 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:27:22.184441
2025-05-29 09:27:22,184 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=27, 秒=22
2025-05-29 09:27:22,185 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:27:22,186 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:27:22.184441 AND Reservation.end_datetime > 2025-05-29 09:27:22.184441
2025-05-29 09:27:22,187 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:27:22,187 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:27:22,187 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:27:22.184441
2025-05-29 09:27:22,188 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:27:22,188 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:27:22,188 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:28:22,192 - root - INFO - 执行预约状态更新任务
2025-05-29 09:28:22,192 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:28:22.192968
2025-05-29 09:28:22,193 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=28, 秒=22
2025-05-29 09:28:22,194 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:28:22,194 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:28:22.192968 AND Reservation.end_datetime > 2025-05-29 09:28:22.192968
2025-05-29 09:28:22,195 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:28:22,195 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:28:22,195 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:28:22.192968
2025-05-29 09:28:22,196 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:28:22,196 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:28:22,197 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:29:22,202 - root - INFO - 执行预约状态更新任务
2025-05-29 09:29:22,202 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:29:22.202600
2025-05-29 09:29:22,202 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=29, 秒=22
2025-05-29 09:29:22,204 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:29:22,204 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:29:22.202600 AND Reservation.end_datetime > 2025-05-29 09:29:22.202600
2025-05-29 09:29:22,205 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:29:22,205 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:29:22,205 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:29:22.202600
2025-05-29 09:29:22,206 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:29:22,206 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:29:22,206 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:30:22,216 - root - INFO - 执行预约状态更新任务
2025-05-29 09:30:22,217 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:30:22.217076
2025-05-29 09:30:22,217 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=30, 秒=22
2025-05-29 09:30:22,218 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:30:22,218 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:30:22.217076 AND Reservation.end_datetime > 2025-05-29 09:30:22.217076
2025-05-29 09:30:22,219 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:30:22,220 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:30:22,220 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:30:22.217076
2025-05-29 09:30:22,221 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:30:22,221 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:30:22,221 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:31:22,233 - root - INFO - 执行预约状态更新任务
2025-05-29 09:31:22,233 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:31:22.233825
2025-05-29 09:31:22,234 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=31, 秒=22
2025-05-29 09:31:22,235 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:31:22,235 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:31:22.233825 AND Reservation.end_datetime > 2025-05-29 09:31:22.233825
2025-05-29 09:31:22,236 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:31:22,236 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:31:22,236 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:31:22.233825
2025-05-29 09:31:22,237 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:31:22,238 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:31:22,238 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:32:22,251 - root - INFO - 执行预约状态更新任务
2025-05-29 09:32:22,251 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:32:22.251545
2025-05-29 09:32:22,251 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=32, 秒=22
2025-05-29 09:32:22,252 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:32:22,253 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:32:22.251545 AND Reservation.end_datetime > 2025-05-29 09:32:22.251545
2025-05-29 09:32:22,254 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:32:22,254 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:32:22,254 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:32:22.251545
2025-05-29 09:32:22,255 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:32:22,255 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:32:22,255 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:33:22,269 - root - INFO - 执行预约状态更新任务
2025-05-29 09:33:22,270 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:33:22.270215
2025-05-29 09:33:22,270 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=33, 秒=22
2025-05-29 09:33:22,271 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:33:22,271 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:33:22.270215 AND Reservation.end_datetime > 2025-05-29 09:33:22.270215
2025-05-29 09:33:22,272 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:33:22,272 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:33:22,272 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:33:22.270215
2025-05-29 09:33:22,273 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:33:22,273 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:33:22,274 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:34:05,819 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 09:34:14,970 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 09:34:16,865 - backend.routes.reservation - INFO - 获取预约详情: 预约码=WM51NAWY, 开始时间=None, 结束时间=None, 预约序号=RN-20250620-9315-5
2025-05-29 09:34:16,865 - backend.routes.reservation - INFO - 提供了预约序号 RN-20250620-9315-5，优先使用预约序号查询
2025-05-29 09:34:16,865 - backend.routes.reservation - INFO - 使用预约序号查询: RN-20250620-9315-5
2025-05-29 09:34:16,866 - backend.routes.reservation - INFO - 通过预约序号直接找到预约: ID=232, 状态=confirmed
2025-05-29 09:34:16,867 - backend.routes.reservation - INFO - 通过预约序号查询成功，返回预约详情: ID=232, 状态=confirmed
2025-05-29 09:34:21,318 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 09:34:22,287 - root - INFO - 执行预约状态更新任务
2025-05-29 09:34:22,287 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:34:22.287973
2025-05-29 09:34:22,288 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=34, 秒=22
2025-05-29 09:34:22,289 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:34:22,289 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:34:22.287973 AND Reservation.end_datetime > 2025-05-29 09:34:22.287973
2025-05-29 09:34:22,290 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:34:22,290 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:34:22,291 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:34:22.287973
2025-05-29 09:34:22,291 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:34:22,291 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:34:22,292 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:35:22,293 - root - INFO - 执行预约状态更新任务
2025-05-29 09:35:22,294 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:35:22.294349
2025-05-29 09:35:22,294 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=35, 秒=22
2025-05-29 09:35:22,296 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:35:22,297 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:35:22.294349 AND Reservation.end_datetime > 2025-05-29 09:35:22.294349
2025-05-29 09:35:22,298 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:35:22,299 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:35:22,300 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:35:22.294349
2025-05-29 09:35:22,302 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:35:22,302 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:35:22,302 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:36:22,310 - root - INFO - 执行预约状态更新任务
2025-05-29 09:36:22,310 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:36:22.310946
2025-05-29 09:36:22,311 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=36, 秒=22
2025-05-29 09:36:22,312 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:36:22,312 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:36:22.310946 AND Reservation.end_datetime > 2025-05-29 09:36:22.310946
2025-05-29 09:36:22,313 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:36:22,313 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:36:22,313 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:36:22.310946
2025-05-29 09:36:22,314 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:36:22,314 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:36:22,315 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:37:22,322 - root - INFO - 执行预约状态更新任务
2025-05-29 09:37:22,323 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:37:22.323212
2025-05-29 09:37:22,323 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=37, 秒=22
2025-05-29 09:37:22,324 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:37:22,324 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:37:22.323212 AND Reservation.end_datetime > 2025-05-29 09:37:22.323212
2025-05-29 09:37:22,325 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:37:22,325 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:37:22,326 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:37:22.323212
2025-05-29 09:37:22,326 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:37:22,327 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:37:22,327 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:38:22,335 - root - INFO - 执行预约状态更新任务
2025-05-29 09:38:22,336 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:38:22.336216
2025-05-29 09:38:22,336 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=38, 秒=22
2025-05-29 09:38:22,337 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:38:22,337 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:38:22.336216 AND Reservation.end_datetime > 2025-05-29 09:38:22.336216
2025-05-29 09:38:22,338 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:38:22,338 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:38:22,339 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:38:22.336216
2025-05-29 09:38:22,339 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:38:22,340 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:38:22,340 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:39:22,343 - root - INFO - 执行预约状态更新任务
2025-05-29 09:39:22,343 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:39:22.343577
2025-05-29 09:39:22,344 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=39, 秒=22
2025-05-29 09:39:22,345 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:39:22,345 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:39:22.343577 AND Reservation.end_datetime > 2025-05-29 09:39:22.343577
2025-05-29 09:39:22,346 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:39:22,347 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:39:22,347 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:39:22.343577
2025-05-29 09:39:22,348 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:39:22,348 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:39:22,348 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:40:11,439 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 09:40:15,303 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 09:40:22,001 - backend.routes.reservation - INFO - 获取预约详情: 预约码=ZFEXH6FR, 开始时间=None, 结束时间=None, 预约序号=RN-20250623-9315-6
2025-05-29 09:40:22,002 - backend.routes.reservation - INFO - 提供了预约序号 RN-20250623-9315-6，优先使用预约序号查询
2025-05-29 09:40:22,002 - backend.routes.reservation - INFO - 使用预约序号查询: RN-20250623-9315-6
2025-05-29 09:40:22,003 - backend.routes.reservation - INFO - 通过预约序号直接找到预约: ID=233, 状态=cancelled
2025-05-29 09:40:22,004 - backend.routes.reservation - INFO - 通过预约序号查询成功，返回预约详情: ID=233, 状态=cancelled
2025-05-29 09:40:22,363 - root - INFO - 执行预约状态更新任务
2025-05-29 09:40:22,363 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:40:22.363915
2025-05-29 09:40:22,364 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=40, 秒=22
2025-05-29 09:40:22,365 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:40:22,365 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:40:22.363915 AND Reservation.end_datetime > 2025-05-29 09:40:22.363915
2025-05-29 09:40:22,366 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:40:22,366 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:40:22,366 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:40:22.363915
2025-05-29 09:40:22,367 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:40:22,367 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:40:22,367 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:40:33,442 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 09:41:22,376 - root - INFO - 执行预约状态更新任务
2025-05-29 09:41:22,377 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:41:22.377516
2025-05-29 09:41:22,377 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=41, 秒=22
2025-05-29 09:41:22,378 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:41:22,378 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:41:22.377516 AND Reservation.end_datetime > 2025-05-29 09:41:22.377516
2025-05-29 09:41:22,379 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:41:22,379 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:41:22,380 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:41:22.377516
2025-05-29 09:41:22,380 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:41:22,381 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:41:22,381 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:42:22,393 - root - INFO - 执行预约状态更新任务
2025-05-29 09:42:22,393 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:42:22.393881
2025-05-29 09:42:22,394 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=42, 秒=22
2025-05-29 09:42:22,395 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:42:22,395 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:42:22.393881 AND Reservation.end_datetime > 2025-05-29 09:42:22.393881
2025-05-29 09:42:22,396 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:42:22,396 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:42:22,396 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:42:22.393881
2025-05-29 09:42:22,397 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:42:22,397 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:42:22,397 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:43:22,406 - root - INFO - 执行预约状态更新任务
2025-05-29 09:43:22,406 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:43:22.406566
2025-05-29 09:43:22,407 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=43, 秒=22
2025-05-29 09:43:22,407 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:43:22,408 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:43:22.406566 AND Reservation.end_datetime > 2025-05-29 09:43:22.406566
2025-05-29 09:43:22,408 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:43:22,409 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:43:22,409 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:43:22.406566
2025-05-29 09:43:22,410 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:43:22,410 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:43:22,410 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:44:22,419 - root - INFO - 执行预约状态更新任务
2025-05-29 09:44:22,419 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:44:22.419823
2025-05-29 09:44:22,420 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=44, 秒=22
2025-05-29 09:44:22,421 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:44:22,421 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:44:22.419823 AND Reservation.end_datetime > 2025-05-29 09:44:22.419823
2025-05-29 09:44:22,422 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:44:22,422 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:44:22,422 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:44:22.419823
2025-05-29 09:44:22,423 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:44:22,423 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:44:22,423 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:45:21,076 - root - INFO - 执行预约状态更新任务
2025-05-29 09:45:21,077 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:45:21.076985
2025-05-29 09:45:21,077 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=45, 秒=21
2025-05-29 09:45:21,078 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:45:21,078 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:45:21.076985 AND Reservation.end_datetime > 2025-05-29 09:45:21.076985
2025-05-29 09:45:21,079 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:45:21,079 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:45:21,079 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:45:21.076985
2025-05-29 09:45:21,080 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:45:21,080 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:45:21,080 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:46:21,091 - root - INFO - 执行预约状态更新任务
2025-05-29 09:46:21,092 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:46:21.092372
2025-05-29 09:46:21,092 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=46, 秒=21
2025-05-29 09:46:21,093 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:46:21,093 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:46:21.092372 AND Reservation.end_datetime > 2025-05-29 09:46:21.092372
2025-05-29 09:46:21,094 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:46:21,094 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:46:21,094 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:46:21.092372
2025-05-29 09:46:21,095 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:46:21,095 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:46:21,095 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:47:21,108 - root - INFO - 执行预约状态更新任务
2025-05-29 09:47:21,108 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:47:21.108683
2025-05-29 09:47:21,109 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=47, 秒=21
2025-05-29 09:47:21,110 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:47:21,110 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:47:21.108683 AND Reservation.end_datetime > 2025-05-29 09:47:21.108683
2025-05-29 09:47:21,111 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:47:21,111 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:47:21,111 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:47:21.108683
2025-05-29 09:47:21,112 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:47:21,112 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:47:21,112 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:48:21,113 - root - INFO - 执行预约状态更新任务
2025-05-29 09:48:21,114 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:48:21.114185
2025-05-29 09:48:21,114 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=48, 秒=21
2025-05-29 09:48:21,115 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:48:21,115 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:48:21.114185 AND Reservation.end_datetime > 2025-05-29 09:48:21.114185
2025-05-29 09:48:21,116 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:48:21,116 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:48:21,117 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:48:21.114185
2025-05-29 09:48:21,117 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:48:21,118 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:48:21,118 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:49:20,342 - root - INFO - 执行日志维护任务
2025-05-29 09:49:20,344 - root - INFO - 日志备份已创建: logs\backups\app-20250529-094920.log
2025-05-29 09:49:20,344 - root - INFO - 执行预约序号重复检查任务
2025-05-29 09:49:20,344 - backend.utils.duplicate_checker - INFO - 开始检查重复的预约序号...
2025-05-29 09:49:20,345 - backend.utils.duplicate_checker - INFO - 没有发现重复的预约序号
2025-05-29 09:49:20,346 - root - INFO - 没有发现重复的预约序号
2025-05-29 09:49:21,126 - root - INFO - 执行预约状态更新任务
2025-05-29 09:49:21,126 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:49:21.126619
2025-05-29 09:49:21,126 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=49, 秒=21
2025-05-29 09:49:21,127 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:49:21,128 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:49:21.126619 AND Reservation.end_datetime > 2025-05-29 09:49:21.126619
2025-05-29 09:49:21,129 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:49:21,129 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:49:21,129 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:49:21.126619
2025-05-29 09:49:21,130 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:49:21,130 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:49:21,130 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:50:21,142 - root - INFO - 执行预约状态更新任务
2025-05-29 09:50:21,142 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:50:21.142695
2025-05-29 09:50:21,143 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=50, 秒=21
2025-05-29 09:50:21,143 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:50:21,144 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:50:21.142695 AND Reservation.end_datetime > 2025-05-29 09:50:21.142695
2025-05-29 09:50:21,145 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:50:21,145 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:50:21,145 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:50:21.142695
2025-05-29 09:50:21,146 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:50:21,146 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:50:21,146 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:51:21,157 - root - INFO - 执行预约状态更新任务
2025-05-29 09:51:21,158 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:51:21.158487
2025-05-29 09:51:21,158 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=51, 秒=21
2025-05-29 09:51:21,160 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:51:21,160 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:51:21.158487 AND Reservation.end_datetime > 2025-05-29 09:51:21.158487
2025-05-29 09:51:21,161 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:51:21,161 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:51:21,161 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:51:21.158487
2025-05-29 09:51:21,162 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:51:21,163 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:51:21,163 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:52:14,880 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 09:52:21,168 - root - INFO - 执行预约状态更新任务
2025-05-29 09:52:21,168 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:52:21.168763
2025-05-29 09:52:21,169 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=52, 秒=21
2025-05-29 09:52:21,169 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:52:21,170 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:52:21.168763 AND Reservation.end_datetime > 2025-05-29 09:52:21.168763
2025-05-29 09:52:21,170 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:52:21,171 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:52:21,171 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:52:21.168763
2025-05-29 09:52:21,171 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:52:21,172 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:52:21,172 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:53:21,176 - root - INFO - 执行预约状态更新任务
2025-05-29 09:53:21,176 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:53:21.176876
2025-05-29 09:53:21,177 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=53, 秒=21
2025-05-29 09:53:21,178 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:53:21,178 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:53:21.176876 AND Reservation.end_datetime > 2025-05-29 09:53:21.176876
2025-05-29 09:53:21,179 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:53:21,179 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:53:21,179 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:53:21.176876
2025-05-29 09:53:21,180 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:53:21,180 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:53:21,180 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:54:21,191 - root - INFO - 执行预约状态更新任务
2025-05-29 09:54:21,191 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:54:21.191863
2025-05-29 09:54:21,192 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=54, 秒=21
2025-05-29 09:54:21,193 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:54:21,193 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:54:21.191863 AND Reservation.end_datetime > 2025-05-29 09:54:21.191863
2025-05-29 09:54:21,194 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:54:21,194 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:54:21,194 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:54:21.191863
2025-05-29 09:54:21,195 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:54:21,195 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:54:21,195 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:55:21,203 - root - INFO - 执行预约状态更新任务
2025-05-29 09:55:21,204 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:55:21.204318
2025-05-29 09:55:21,204 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=55, 秒=21
2025-05-29 09:55:21,205 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:55:21,205 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:55:21.204318 AND Reservation.end_datetime > 2025-05-29 09:55:21.204318
2025-05-29 09:55:21,206 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:55:21,206 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:55:21,206 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:55:21.204318
2025-05-29 09:55:21,207 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:55:21,207 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:55:21,208 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:56:21,222 - root - INFO - 执行预约状态更新任务
2025-05-29 09:56:21,223 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:56:21.223506
2025-05-29 09:56:21,223 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=56, 秒=21
2025-05-29 09:56:21,224 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:56:21,225 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:56:21.223506 AND Reservation.end_datetime > 2025-05-29 09:56:21.223506
2025-05-29 09:56:21,226 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:56:21,226 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:56:21,226 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:56:21.223506
2025-05-29 09:56:21,227 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:56:21,227 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:56:21,227 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:57:21,229 - root - INFO - 执行预约状态更新任务
2025-05-29 09:57:21,229 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:57:21.229640
2025-05-29 09:57:21,230 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=57, 秒=21
2025-05-29 09:57:21,230 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:57:21,231 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:57:21.229640 AND Reservation.end_datetime > 2025-05-29 09:57:21.229640
2025-05-29 09:57:21,231 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:57:21,232 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:57:21,232 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:57:21.229640
2025-05-29 09:57:21,233 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:57:21,233 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:57:21,233 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:58:21,239 - root - INFO - 执行预约状态更新任务
2025-05-29 09:58:21,239 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:58:21.239661
2025-05-29 09:58:21,240 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=58, 秒=21
2025-05-29 09:58:21,240 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:58:21,241 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:58:21.239661 AND Reservation.end_datetime > 2025-05-29 09:58:21.239661
2025-05-29 09:58:21,241 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:58:21,242 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:58:21,242 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:58:21.239661
2025-05-29 09:58:21,243 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:58:21,243 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:58:21,243 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:59:21,250 - root - INFO - 执行预约状态更新任务
2025-05-29 09:59:21,251 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:59:21.251375
2025-05-29 09:59:21,251 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=59, 秒=21
2025-05-29 09:59:21,252 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:59:21,253 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:59:21.251375 AND Reservation.end_datetime > 2025-05-29 09:59:21.251375
2025-05-29 09:59:21,253 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:59:21,254 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:59:21,254 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:59:21.251375
2025-05-29 09:59:21,255 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:59:21,255 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:59:21,255 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:59:23,854 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 10:00:21,264 - root - INFO - 执行预约状态更新任务
2025-05-29 10:00:21,264 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:00:21.264785
2025-05-29 10:00:21,265 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=0, 秒=21
2025-05-29 10:00:21,265 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:00:21,266 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:00:21.264785 AND Reservation.end_datetime > 2025-05-29 10:00:21.264785
2025-05-29 10:00:21,267 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:00:21,267 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:00:21,267 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:00:21.264785
2025-05-29 10:00:21,268 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:00:21,268 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:00:21,268 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:00:34,093 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 10:00:38,223 - backend.routes.reservation - INFO - 获取预约详情: 预约码=LM2VEHSC, 开始时间=None, 结束时间=None, 预约序号=RN-20250626-8071-22
2025-05-29 10:00:38,224 - backend.routes.reservation - INFO - 提供了预约序号 RN-20250626-8071-22，优先使用预约序号查询
2025-05-29 10:00:38,224 - backend.routes.reservation - INFO - 使用预约序号查询: RN-20250626-8071-22
2025-05-29 10:00:38,225 - backend.routes.reservation - INFO - 通过预约序号直接找到预约: ID=161, 状态=cancelled
2025-05-29 10:00:38,226 - backend.routes.reservation - INFO - 通过预约序号查询成功，返回预约详情: ID=161, 状态=cancelled
2025-05-29 10:00:42,839 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 10:00:46,814 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 10:00:50,903 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 10:00:53,876 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 10:01:08,569 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 10:01:21,274 - root - INFO - 执行预约状态更新任务
2025-05-29 10:01:21,274 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:01:21.274906
2025-05-29 10:01:21,275 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=1, 秒=21
2025-05-29 10:01:21,276 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:01:21,276 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:01:21.274906 AND Reservation.end_datetime > 2025-05-29 10:01:21.274906
2025-05-29 10:01:21,277 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:01:21,277 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:01:21,278 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:01:21.274906
2025-05-29 10:01:21,279 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:01:21,279 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:01:21,279 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:02:21,289 - root - INFO - 执行预约状态更新任务
2025-05-29 10:02:21,289 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:02:21.289839
2025-05-29 10:02:21,290 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=2, 秒=21
2025-05-29 10:02:21,291 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:02:21,291 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:02:21.289839 AND Reservation.end_datetime > 2025-05-29 10:02:21.289839
2025-05-29 10:02:21,292 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:02:21,292 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:02:21,292 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:02:21.289839
2025-05-29 10:02:21,293 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:02:21,293 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:02:21,293 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:02:46,157 - backend.routes.reservation - INFO - 获取预约详情: 预约码=L44YME4K, 开始时间=None, 结束时间=None, 预约序号=None
2025-05-29 10:02:46,159 - backend.routes.reservation - INFO - 尝试获取与预约码匹配的预约
2025-05-29 10:02:46,160 - backend.routes.reservation - INFO - 没有日期参数，直接查找最近的预约
2025-05-29 10:02:46,161 - backend.routes.reservation - INFO - 找到已确认的预约: ID=222
2025-05-29 10:02:46,161 - backend.routes.reservation - INFO - 成功获取预约: ID=222, 状态=confirmed, 开始时间=2025-06-03 02:00:00, 结束时间=2025-06-03 09:00:00, 预约序号=RN-20250516-8875
2025-05-29 10:02:46,197 - backend.routes.reservation - INFO - 获取预约详情: 预约码=L44YME4K, 开始时间=None, 结束时间=None, 预约序号=None
2025-05-29 10:02:46,198 - backend.routes.reservation - INFO - 尝试获取与预约码匹配的预约
2025-05-29 10:02:46,198 - backend.routes.reservation - INFO - 没有日期参数，直接查找最近的预约
2025-05-29 10:02:46,199 - backend.routes.reservation - INFO - 找到已确认的预约: ID=222
2025-05-29 10:02:46,200 - backend.routes.reservation - INFO - 成功获取预约: ID=222, 状态=confirmed, 开始时间=2025-06-03 02:00:00, 结束时间=2025-06-03 09:00:00, 预约序号=RN-20250516-8875
2025-05-29 10:03:21,299 - root - INFO - 执行预约状态更新任务
2025-05-29 10:03:21,300 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:03:21.300380
2025-05-29 10:03:21,301 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=3, 秒=21
2025-05-29 10:03:21,303 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:03:21,304 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:03:21.300380 AND Reservation.end_datetime > 2025-05-29 10:03:21.300380
2025-05-29 10:03:21,306 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:03:21,306 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:03:21,307 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:03:21.300380
2025-05-29 10:03:21,308 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:03:21,308 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:03:21,309 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:04:21,324 - root - INFO - 执行预约状态更新任务
2025-05-29 10:04:21,325 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:04:21.325038
2025-05-29 10:04:21,325 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=4, 秒=21
2025-05-29 10:04:21,326 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:04:21,326 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:04:21.325038 AND Reservation.end_datetime > 2025-05-29 10:04:21.325038
2025-05-29 10:04:21,327 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:04:21,327 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:04:21,327 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:04:21.325038
2025-05-29 10:04:21,328 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:04:21,328 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:04:21,328 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:05:21,337 - root - INFO - 执行预约状态更新任务
2025-05-29 10:05:21,338 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:05:21.338086
2025-05-29 10:05:21,338 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=5, 秒=21
2025-05-29 10:05:21,339 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:05:21,339 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:05:21.338086 AND Reservation.end_datetime > 2025-05-29 10:05:21.338086
2025-05-29 10:05:21,340 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:05:21,340 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:05:21,341 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:05:21.338086
2025-05-29 10:05:21,341 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:05:21,342 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:05:21,342 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:06:21,346 - root - INFO - 执行预约状态更新任务
2025-05-29 10:06:21,347 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:06:21.347156
2025-05-29 10:06:21,347 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=6, 秒=21
2025-05-29 10:06:21,348 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:06:21,348 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:06:21.347156 AND Reservation.end_datetime > 2025-05-29 10:06:21.347156
2025-05-29 10:06:21,349 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:06:21,349 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:06:21,350 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:06:21.347156
2025-05-29 10:06:21,350 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:06:21,351 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:06:21,351 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:07:21,354 - root - INFO - 执行预约状态更新任务
2025-05-29 10:07:21,355 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:07:21.355176
2025-05-29 10:07:21,355 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=7, 秒=21
2025-05-29 10:07:21,356 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:07:21,356 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:07:21.355176 AND Reservation.end_datetime > 2025-05-29 10:07:21.355176
2025-05-29 10:07:21,357 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:07:21,357 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:07:21,358 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:07:21.355176
2025-05-29 10:07:21,358 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:07:21,359 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:07:21,359 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:08:21,371 - root - INFO - 执行预约状态更新任务
2025-05-29 10:08:21,372 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:08:21.372202
2025-05-29 10:08:21,372 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=8, 秒=21
2025-05-29 10:08:21,373 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:08:21,373 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:08:21.372202 AND Reservation.end_datetime > 2025-05-29 10:08:21.372202
2025-05-29 10:08:21,374 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:08:21,374 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:08:21,374 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:08:21.372202
2025-05-29 10:08:21,375 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:08:21,375 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:08:21,375 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:09:21,386 - root - INFO - 执行预约状态更新任务
2025-05-29 10:09:21,387 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:09:21.387503
2025-05-29 10:09:21,387 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=9, 秒=21
2025-05-29 10:09:21,388 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:09:21,388 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:09:21.387503 AND Reservation.end_datetime > 2025-05-29 10:09:21.387503
2025-05-29 10:09:21,389 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:09:21,389 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:09:21,390 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:09:21.387503
2025-05-29 10:09:21,390 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:09:21,391 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:09:21,391 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:10:21,396 - root - INFO - 执行预约状态更新任务
2025-05-29 10:10:21,396 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:10:21.396832
2025-05-29 10:10:21,397 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=10, 秒=21
2025-05-29 10:10:21,398 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:10:21,398 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:10:21.396832 AND Reservation.end_datetime > 2025-05-29 10:10:21.396832
2025-05-29 10:10:21,399 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:10:21,399 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:10:21,399 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:10:21.396832
2025-05-29 10:10:21,400 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:10:21,400 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:10:21,401 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:11:21,404 - root - INFO - 执行预约状态更新任务
2025-05-29 10:11:21,404 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:11:21.404540
2025-05-29 10:11:21,404 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=11, 秒=21
2025-05-29 10:11:21,405 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:11:21,405 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:11:21.404540 AND Reservation.end_datetime > 2025-05-29 10:11:21.404540
2025-05-29 10:11:21,406 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:11:21,406 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:11:21,406 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:11:21.404540
2025-05-29 10:11:21,407 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:11:21,407 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:11:21,408 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:12:21,411 - root - INFO - 执行预约状态更新任务
2025-05-29 10:12:21,411 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:12:21.411741
2025-05-29 10:12:21,412 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=12, 秒=21
2025-05-29 10:12:21,412 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:12:21,413 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:12:21.411741 AND Reservation.end_datetime > 2025-05-29 10:12:21.411741
2025-05-29 10:12:21,413 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:12:21,414 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:12:21,414 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:12:21.411741
2025-05-29 10:12:21,414 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:12:21,415 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:12:21,415 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:13:21,417 - root - INFO - 执行预约状态更新任务
2025-05-29 10:13:21,418 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:13:21.418299
2025-05-29 10:13:21,418 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=13, 秒=21
2025-05-29 10:13:21,419 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:13:21,419 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:13:21.418299 AND Reservation.end_datetime > 2025-05-29 10:13:21.418299
2025-05-29 10:13:21,420 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:13:21,420 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:13:21,421 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:13:21.418299
2025-05-29 10:13:21,421 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:13:21,421 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:13:21,422 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:14:21,425 - root - INFO - 执行预约状态更新任务
2025-05-29 10:14:21,426 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:14:21.426018
2025-05-29 10:14:21,426 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=14, 秒=21
2025-05-29 10:14:21,427 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:14:21,427 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:14:21.426018 AND Reservation.end_datetime > 2025-05-29 10:14:21.426018
2025-05-29 10:14:21,428 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:14:21,429 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:14:21,429 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:14:21.426018
2025-05-29 10:14:21,430 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:14:21,430 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:14:21,431 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:15:21,444 - root - INFO - 执行预约状态更新任务
2025-05-29 10:15:21,445 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:15:21.445307
2025-05-29 10:15:21,445 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=15, 秒=21
2025-05-29 10:15:21,446 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:15:21,446 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:15:21.445307 AND Reservation.end_datetime > 2025-05-29 10:15:21.445307
2025-05-29 10:15:21,447 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:15:21,447 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:15:21,447 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:15:21.445307
2025-05-29 10:15:21,448 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:15:21,448 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:15:21,449 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:16:21,451 - root - INFO - 执行预约状态更新任务
2025-05-29 10:16:21,452 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:16:21.452096
2025-05-29 10:16:21,452 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=16, 秒=21
2025-05-29 10:16:21,453 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:16:21,453 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:16:21.452096 AND Reservation.end_datetime > 2025-05-29 10:16:21.452096
2025-05-29 10:16:21,454 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:16:21,455 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:16:21,455 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:16:21.452096
2025-05-29 10:16:21,456 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:16:21,456 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:16:21,456 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:17:21,458 - root - INFO - 执行预约状态更新任务
2025-05-29 10:17:21,458 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:17:21.458895
2025-05-29 10:17:21,459 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=17, 秒=21
2025-05-29 10:17:21,460 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:17:21,460 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:17:21.458895 AND Reservation.end_datetime > 2025-05-29 10:17:21.458895
2025-05-29 10:17:21,461 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:17:21,461 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:17:21,462 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:17:21.458895
2025-05-29 10:17:21,462 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:17:21,463 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:17:21,463 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:18:21,475 - root - INFO - 执行预约状态更新任务
2025-05-29 10:18:21,476 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:18:21.476190
2025-05-29 10:18:21,476 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=18, 秒=21
2025-05-29 10:18:21,477 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:18:21,477 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:18:21.476190 AND Reservation.end_datetime > 2025-05-29 10:18:21.476190
2025-05-29 10:18:21,478 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:18:21,478 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:18:21,479 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:18:21.476190
2025-05-29 10:18:21,479 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:18:21,480 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:18:21,480 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:19:21,491 - root - INFO - 执行预约状态更新任务
2025-05-29 10:19:21,492 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:19:21.492192
2025-05-29 10:19:21,492 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=19, 秒=21
2025-05-29 10:19:21,493 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:19:21,493 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:19:21.492192 AND Reservation.end_datetime > 2025-05-29 10:19:21.492192
2025-05-29 10:19:21,494 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:19:21,494 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:19:21,494 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:19:21.492192
2025-05-29 10:19:21,495 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:19:21,495 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:19:21,495 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:20:21,497 - root - INFO - 执行预约状态更新任务
2025-05-29 10:20:21,497 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:20:21.497668
2025-05-29 10:20:21,497 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=20, 秒=21
2025-05-29 10:20:21,499 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:20:21,499 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:20:21.497668 AND Reservation.end_datetime > 2025-05-29 10:20:21.497668
2025-05-29 10:20:21,500 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:20:21,500 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:20:21,500 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:20:21.497668
2025-05-29 10:20:21,501 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:20:21,501 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:20:21,501 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:21:21,502 - root - INFO - 执行预约状态更新任务
2025-05-29 10:21:21,503 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:21:21.503544
2025-05-29 10:21:21,503 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=21, 秒=21
2025-05-29 10:21:21,504 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:21:21,505 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:21:21.503544 AND Reservation.end_datetime > 2025-05-29 10:21:21.503544
2025-05-29 10:21:21,505 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:21:21,506 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:21:21,506 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:21:21.503544
2025-05-29 10:21:21,507 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:21:21,507 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:21:21,507 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:22:21,522 - root - INFO - 执行预约状态更新任务
2025-05-29 10:22:21,523 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:22:21.523392
2025-05-29 10:22:21,523 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=22, 秒=21
2025-05-29 10:22:21,524 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:22:21,524 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:22:21.523392 AND Reservation.end_datetime > 2025-05-29 10:22:21.523392
2025-05-29 10:22:21,525 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:22:21,525 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:22:21,526 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:22:21.523392
2025-05-29 10:22:21,526 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:22:21,527 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:22:21,527 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:23:21,541 - root - INFO - 执行预约状态更新任务
2025-05-29 10:23:21,542 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:23:21.542007
2025-05-29 10:23:21,542 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=23, 秒=21
2025-05-29 10:23:21,543 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:23:21,543 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:23:21.542007 AND Reservation.end_datetime > 2025-05-29 10:23:21.542007
2025-05-29 10:23:21,544 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:23:21,544 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:23:21,544 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:23:21.542007
2025-05-29 10:23:21,545 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:23:21,545 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:23:21,545 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:24:21,559 - root - INFO - 执行预约状态更新任务
2025-05-29 10:24:21,560 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:24:21.560074
2025-05-29 10:24:21,560 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=24, 秒=21
2025-05-29 10:24:21,561 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:24:21,561 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:24:21.560074 AND Reservation.end_datetime > 2025-05-29 10:24:21.560074
2025-05-29 10:24:21,562 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:24:21,562 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:24:21,562 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:24:21.560074
2025-05-29 10:24:21,563 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:24:21,563 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:24:21,564 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:25:21,565 - root - INFO - 执行预约状态更新任务
2025-05-29 10:25:21,565 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:25:21.565568
2025-05-29 10:25:21,565 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=25, 秒=21
2025-05-29 10:25:21,566 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:25:21,567 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:25:21.565568 AND Reservation.end_datetime > 2025-05-29 10:25:21.565568
2025-05-29 10:25:21,568 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:25:21,568 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:25:21,568 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:25:21.565568
2025-05-29 10:25:21,569 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:25:21,569 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:25:21,569 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:26:21,584 - root - INFO - 执行预约状态更新任务
2025-05-29 10:26:21,585 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:26:21.585442
2025-05-29 10:26:21,585 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=26, 秒=21
2025-05-29 10:26:21,586 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:26:21,587 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:26:21.585442 AND Reservation.end_datetime > 2025-05-29 10:26:21.585442
2025-05-29 10:26:21,587 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:26:21,588 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:26:21,588 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:26:21.585442
2025-05-29 10:26:21,588 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:26:21,589 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:26:21,589 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:27:21,590 - root - INFO - 执行预约状态更新任务
2025-05-29 10:27:21,590 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:27:21.590840
2025-05-29 10:27:21,591 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=27, 秒=21
2025-05-29 10:27:21,592 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:27:21,592 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:27:21.590840 AND Reservation.end_datetime > 2025-05-29 10:27:21.590840
2025-05-29 10:27:21,593 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:27:21,593 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:27:21,593 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:27:21.590840
2025-05-29 10:27:21,594 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:27:21,594 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:27:21,594 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:28:21,607 - root - INFO - 执行预约状态更新任务
2025-05-29 10:28:21,608 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:28:21.608532
2025-05-29 10:28:21,608 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=28, 秒=21
2025-05-29 10:28:21,610 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:28:21,610 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:28:21.608532 AND Reservation.end_datetime > 2025-05-29 10:28:21.608532
2025-05-29 10:28:21,611 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:28:21,611 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:28:21,611 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:28:21.608532
2025-05-29 10:28:21,612 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:28:21,612 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:28:21,613 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:29:21,614 - root - INFO - 执行预约状态更新任务
2025-05-29 10:29:21,614 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:29:21.614962
2025-05-29 10:29:21,615 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=29, 秒=21
2025-05-29 10:29:21,616 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:29:21,616 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:29:21.614962 AND Reservation.end_datetime > 2025-05-29 10:29:21.614962
2025-05-29 10:29:21,617 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:29:21,617 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:29:21,617 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:29:21.614962
2025-05-29 10:29:21,618 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:29:21,618 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:29:21,619 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:30:21,631 - root - INFO - 执行预约状态更新任务
2025-05-29 10:30:21,632 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:30:21.632087
2025-05-29 10:30:21,632 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=30, 秒=21
2025-05-29 10:30:21,633 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:30:21,633 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:30:21.632087 AND Reservation.end_datetime > 2025-05-29 10:30:21.632087
2025-05-29 10:30:21,634 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:30:21,634 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:30:21,635 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:30:21.632087
2025-05-29 10:30:21,635 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:30:21,636 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:30:21,636 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:30:43,534 - root - INFO - 后台任务已取消
2025-05-29 10:30:43,534 - root - INFO - 应用关闭 / Application shut down
2025-05-29 10:30:43,891 - root - INFO - 设备预定系统启动中...
2025-05-29 10:30:44,357 - root - INFO - FastAPI应用初始化中...
2025-05-29 10:30:44,360 - backend.i18n - INFO - 国际化设置成功
2025-05-29 10:30:44,861 - root - INFO - 应用启动 / Application started
2025-05-29 10:30:44,864 - backend.database - INFO - 数据库初始化成功
2025-05-29 10:30:44,864 - root - INFO - 后台任务已启动
2025-05-29 10:30:44,865 - root - INFO - 执行预约状态更新任务
2025-05-29 10:30:44,865 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:30:44.865427
2025-05-29 10:30:44,865 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=30, 秒=44
2025-05-29 10:30:44,880 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:30:44,880 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:30:44.865427 AND Reservation.end_datetime > 2025-05-29 10:30:44.865427
2025-05-29 10:30:44,882 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:30:44,883 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:30:44,883 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:30:44.865427
2025-05-29 10:30:44,885 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:30:44,885 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:30:44,885 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:31:44,898 - root - INFO - 执行预约状态更新任务
2025-05-29 10:31:44,899 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:31:44.899047
2025-05-29 10:31:44,899 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=31, 秒=44
2025-05-29 10:31:44,900 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:31:44,900 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:31:44.899047 AND Reservation.end_datetime > 2025-05-29 10:31:44.899047
2025-05-29 10:31:44,901 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:31:44,901 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:31:44,901 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:31:44.899047
2025-05-29 10:31:44,902 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:31:44,902 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:31:44,903 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:32:14,487 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 10:32:20,155 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 10:32:21,300 - backend.routes.reservation - INFO - 获取预约详情: 预约码=RN-20250516-8875, 开始时间=None, 结束时间=None, 预约序号=None
2025-05-29 10:32:21,304 - backend.routes.reservation - INFO - 尝试获取与预约码匹配的预约
2025-05-29 10:32:21,304 - backend.routes.reservation - INFO - 没有日期参数，直接查找最近的预约
2025-05-29 10:32:21,306 - backend.routes.reservation - WARNING - 未找到与预约码匹配的预约: RN-20250516-8875
2025-05-29 10:32:26,435 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 10:32:29,007 - backend.routes.reservation - INFO - 获取预约详情: 预约码=RN-20250516-8875, 开始时间=None, 结束时间=None, 预约序号=None
2025-05-29 10:32:29,009 - backend.routes.reservation - INFO - 尝试获取与预约码匹配的预约
2025-05-29 10:32:29,009 - backend.routes.reservation - INFO - 没有日期参数，直接查找最近的预约
2025-05-29 10:32:29,010 - backend.routes.reservation - WARNING - 未找到与预约码匹配的预约: RN-20250516-8875
2025-05-29 10:32:30,886 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 10:32:33,382 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 10:32:44,904 - root - INFO - 执行预约状态更新任务
2025-05-29 10:32:44,904 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:32:44.904616
2025-05-29 10:32:44,904 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=32, 秒=44
2025-05-29 10:32:44,905 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:32:44,906 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:32:44.904616 AND Reservation.end_datetime > 2025-05-29 10:32:44.904616
2025-05-29 10:32:44,906 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:32:44,907 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:32:44,907 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:32:44.904616
2025-05-29 10:32:44,908 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:32:44,908 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:32:44,908 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:33:44,916 - root - INFO - 执行预约状态更新任务
2025-05-29 10:33:44,916 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:33:44.916714
2025-05-29 10:33:44,917 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=33, 秒=44
2025-05-29 10:33:44,917 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:33:44,918 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:33:44.916714 AND Reservation.end_datetime > 2025-05-29 10:33:44.916714
2025-05-29 10:33:44,918 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:33:44,919 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:33:44,919 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:33:44.916714
2025-05-29 10:33:44,920 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:33:44,920 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:33:44,920 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:34:44,921 - root - INFO - 执行预约状态更新任务
2025-05-29 10:34:44,922 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:34:44.922387
2025-05-29 10:34:44,922 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=34, 秒=44
2025-05-29 10:34:44,923 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:34:44,924 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:34:44.922387 AND Reservation.end_datetime > 2025-05-29 10:34:44.922387
2025-05-29 10:34:44,925 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:34:44,925 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:34:44,925 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:34:44.922387
2025-05-29 10:34:44,926 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:34:44,926 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:34:44,927 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:35:44,940 - root - INFO - 执行预约状态更新任务
2025-05-29 10:35:44,940 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:35:44.940670
2025-05-29 10:35:44,941 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=35, 秒=44
2025-05-29 10:35:44,942 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:35:44,942 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:35:44.940670 AND Reservation.end_datetime > 2025-05-29 10:35:44.940670
2025-05-29 10:35:44,943 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:35:44,943 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:35:44,944 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:35:44.940670
2025-05-29 10:35:44,944 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:35:44,944 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:35:44,945 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:36:44,959 - root - INFO - 执行预约状态更新任务
2025-05-29 10:36:44,959 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:36:44.959951
2025-05-29 10:36:44,960 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=36, 秒=44
2025-05-29 10:36:44,961 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:36:44,961 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:36:44.959951 AND Reservation.end_datetime > 2025-05-29 10:36:44.959951
2025-05-29 10:36:44,962 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:36:44,962 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:36:44,963 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:36:44.959951
2025-05-29 10:36:44,963 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:36:44,964 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:36:44,964 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:37:44,965 - root - INFO - 执行预约状态更新任务
2025-05-29 10:37:44,965 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:37:44.965811
2025-05-29 10:37:44,966 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=37, 秒=44
2025-05-29 10:37:44,967 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:37:44,967 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:37:44.965811 AND Reservation.end_datetime > 2025-05-29 10:37:44.965811
2025-05-29 10:37:44,968 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:37:44,968 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:37:44,968 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:37:44.965811
2025-05-29 10:37:44,969 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:37:44,969 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:37:44,969 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:38:44,984 - root - INFO - 执行预约状态更新任务
2025-05-29 10:38:44,985 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:38:44.985435
2025-05-29 10:38:44,985 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=38, 秒=44
2025-05-29 10:38:44,986 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:38:44,986 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:38:44.985435 AND Reservation.end_datetime > 2025-05-29 10:38:44.985435
2025-05-29 10:38:44,987 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:38:44,987 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:38:44,988 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:38:44.985435
2025-05-29 10:38:44,989 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:38:44,989 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:38:44,989 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:39:45,001 - root - INFO - 执行预约状态更新任务
2025-05-29 10:39:45,001 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:39:45.001922
2025-05-29 10:39:45,002 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=39, 秒=45
2025-05-29 10:39:45,003 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:39:45,003 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:39:45.001922 AND Reservation.end_datetime > 2025-05-29 10:39:45.001922
2025-05-29 10:39:45,004 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:39:45,004 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:39:45,005 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:39:45.001922
2025-05-29 10:39:45,005 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:39:45,005 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:39:45,006 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:40:45,015 - root - INFO - 执行预约状态更新任务
2025-05-29 10:40:45,015 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:40:45.015723
2025-05-29 10:40:45,016 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=40, 秒=45
2025-05-29 10:40:45,017 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:40:45,017 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:40:45.015723 AND Reservation.end_datetime > 2025-05-29 10:40:45.015723
2025-05-29 10:40:45,018 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:40:45,018 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:40:45,019 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:40:45.015723
2025-05-29 10:40:45,019 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:40:45,019 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:40:45,020 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:41:45,023 - root - INFO - 执行预约状态更新任务
2025-05-29 10:41:45,023 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:41:45.023920
2025-05-29 10:41:45,024 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=41, 秒=45
2025-05-29 10:41:45,025 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:41:45,025 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:41:45.023920 AND Reservation.end_datetime > 2025-05-29 10:41:45.023920
2025-05-29 10:41:45,026 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:41:45,026 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:41:45,026 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:41:45.023920
2025-05-29 10:41:45,027 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:41:45,027 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:41:45,027 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:42:45,028 - root - INFO - 执行预约状态更新任务
2025-05-29 10:42:45,029 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:42:45.029246
2025-05-29 10:42:45,029 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=42, 秒=45
2025-05-29 10:42:45,030 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:42:45,030 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:42:45.029246 AND Reservation.end_datetime > 2025-05-29 10:42:45.029246
2025-05-29 10:42:45,031 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:42:45,031 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:42:45,031 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:42:45.029246
2025-05-29 10:42:45,032 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:42:45,032 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:42:45,032 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:43:45,036 - root - INFO - 执行预约状态更新任务
2025-05-29 10:43:45,037 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:43:45.037524
2025-05-29 10:43:45,037 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=43, 秒=45
2025-05-29 10:43:45,038 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:43:45,038 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:43:45.037524 AND Reservation.end_datetime > 2025-05-29 10:43:45.037524
2025-05-29 10:43:45,039 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:43:45,039 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:43:45,040 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:43:45.037524
2025-05-29 10:43:45,040 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:43:45,041 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:43:45,041 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:44:45,051 - root - INFO - 执行预约状态更新任务
2025-05-29 10:44:45,051 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:44:45.051754
2025-05-29 10:44:45,052 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=44, 秒=45
2025-05-29 10:44:45,053 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:44:45,053 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:44:45.051754 AND Reservation.end_datetime > 2025-05-29 10:44:45.051754
2025-05-29 10:44:45,054 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:44:45,054 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:44:45,054 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:44:45.051754
2025-05-29 10:44:45,055 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:44:45,055 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:44:45,055 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:45:45,064 - root - INFO - 执行预约状态更新任务
2025-05-29 10:45:45,064 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:45:45.064527
2025-05-29 10:45:45,064 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=45, 秒=45
2025-05-29 10:45:45,065 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:45:45,065 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:45:45.064527 AND Reservation.end_datetime > 2025-05-29 10:45:45.064527
2025-05-29 10:45:45,066 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:45:45,066 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:45:45,066 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:45:45.064527
2025-05-29 10:45:45,067 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:45:45,067 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:45:45,067 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:46:45,075 - root - INFO - 执行预约状态更新任务
2025-05-29 10:46:45,076 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:46:45.076057
2025-05-29 10:46:45,076 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=46, 秒=45
2025-05-29 10:46:45,077 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:46:45,077 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:46:45.076057 AND Reservation.end_datetime > 2025-05-29 10:46:45.076057
2025-05-29 10:46:45,078 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:46:45,078 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:46:45,078 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:46:45.076057
2025-05-29 10:46:45,079 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:46:45,079 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:46:45,080 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:47:45,093 - root - INFO - 执行预约状态更新任务
2025-05-29 10:47:45,093 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:47:45.093606
2025-05-29 10:47:45,093 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=47, 秒=45
2025-05-29 10:47:45,095 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:47:45,095 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:47:45.093606 AND Reservation.end_datetime > 2025-05-29 10:47:45.093606
2025-05-29 10:47:45,096 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:47:45,096 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:47:45,096 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:47:45.093606
2025-05-29 10:47:45,097 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:47:45,097 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:47:45,097 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:48:45,111 - root - INFO - 执行预约状态更新任务
2025-05-29 10:48:45,112 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:48:45.112442
2025-05-29 10:48:45,112 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=48, 秒=45
2025-05-29 10:48:45,113 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:48:45,114 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:48:45.112442 AND Reservation.end_datetime > 2025-05-29 10:48:45.112442
2025-05-29 10:48:45,115 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:48:45,115 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:48:45,115 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:48:45.112442
2025-05-29 10:48:45,116 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:48:45,116 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:48:45,116 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:49:45,129 - root - INFO - 执行预约状态更新任务
2025-05-29 10:49:45,130 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:49:45.130197
2025-05-29 10:49:45,130 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=49, 秒=45
2025-05-29 10:49:45,131 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:49:45,131 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:49:45.130197 AND Reservation.end_datetime > 2025-05-29 10:49:45.130197
2025-05-29 10:49:45,132 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:49:45,132 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:49:45,132 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:49:45.130197
2025-05-29 10:49:45,133 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:49:45,133 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:49:45,133 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:50:45,145 - root - INFO - 执行预约状态更新任务
2025-05-29 10:50:45,145 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:50:45.145555
2025-05-29 10:50:45,145 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=50, 秒=45
2025-05-29 10:50:45,146 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:50:45,147 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:50:45.145555 AND Reservation.end_datetime > 2025-05-29 10:50:45.145555
2025-05-29 10:50:45,147 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:50:45,148 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:50:45,148 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:50:45.145555
2025-05-29 10:50:45,149 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:50:45,149 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:50:45,149 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:51:45,150 - root - INFO - 执行预约状态更新任务
2025-05-29 10:51:45,151 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:51:45.151264
2025-05-29 10:51:45,151 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=51, 秒=45
2025-05-29 10:51:45,153 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:51:45,153 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:51:45.151264 AND Reservation.end_datetime > 2025-05-29 10:51:45.151264
2025-05-29 10:51:45,154 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:51:45,154 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:51:45,154 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:51:45.151264
2025-05-29 10:51:45,155 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:51:45,155 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:51:45,156 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:52:45,161 - root - INFO - 执行预约状态更新任务
2025-05-29 10:52:45,162 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:52:45.162492
2025-05-29 10:52:45,162 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=52, 秒=45
2025-05-29 10:52:45,163 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:52:45,164 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:52:45.162492 AND Reservation.end_datetime > 2025-05-29 10:52:45.162492
2025-05-29 10:52:45,164 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:52:45,165 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:52:45,165 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:52:45.162492
2025-05-29 10:52:45,166 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:52:45,166 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:52:45,166 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:53:45,177 - root - INFO - 执行预约状态更新任务
2025-05-29 10:53:45,178 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:53:45.178313
2025-05-29 10:53:45,178 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=53, 秒=45
2025-05-29 10:53:45,179 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:53:45,179 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:53:45.178313 AND Reservation.end_datetime > 2025-05-29 10:53:45.178313
2025-05-29 10:53:45,180 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:53:45,180 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:53:45,180 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:53:45.178313
2025-05-29 10:53:45,181 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:53:45,181 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:53:45,182 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:54:45,190 - root - INFO - 执行预约状态更新任务
2025-05-29 10:54:45,191 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:54:45.191311
2025-05-29 10:54:45,191 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=54, 秒=45
2025-05-29 10:54:45,192 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:54:45,192 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:54:45.191311 AND Reservation.end_datetime > 2025-05-29 10:54:45.191311
2025-05-29 10:54:45,193 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:54:45,193 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:54:45,193 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:54:45.191311
2025-05-29 10:54:45,194 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:54:45,195 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:54:45,195 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:55:45,207 - root - INFO - 执行预约状态更新任务
2025-05-29 10:55:45,207 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:55:45.207706
2025-05-29 10:55:45,208 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=55, 秒=45
2025-05-29 10:55:45,209 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:55:45,209 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:55:45.207706 AND Reservation.end_datetime > 2025-05-29 10:55:45.207706
2025-05-29 10:55:45,210 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:55:45,210 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:55:45,211 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:55:45.207706
2025-05-29 10:55:45,211 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:55:45,212 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:55:45,212 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:55:48,267 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 10:55:51,062 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 10:55:54,439 - backend.routes.reservation - INFO - [API调用] 通过预约序号获取预约详情: 预约序号=RN-20250516-8875
2025-05-29 10:55:54,444 - backend.routes.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=222, 状态=confirmed, 预约序号=RN-20250516-8875
2025-05-29 10:55:54,445 - backend.routes.reservation - INFO - [API响应] 成功获取预约: ID=222, 状态=confirmed, 开始时间=2025-06-03 02:00:00, 结束时间=2025-06-03 09:00:00, 预约序号=RN-20250516-8875
2025-05-29 10:56:10,567 - backend.routes.crud.reservation - INFO - [数据库查询] 获取预约: 预约码=L44YME4K, 预约序号=RN-20250516-8875
2025-05-29 10:56:10,568 - backend.routes.crud.reservation - INFO - [数据库查询] 使用预约序号查询: RN-20250516-8875
2025-05-29 10:56:10,569 - backend.routes.crud.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=222, 状态=confirmed
2025-05-29 10:56:45,219 - root - INFO - 执行预约状态更新任务
2025-05-29 10:56:45,220 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:56:45.220299
2025-05-29 10:56:45,220 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=56, 秒=45
2025-05-29 10:56:45,221 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:56:45,222 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:56:45.220299 AND Reservation.end_datetime > 2025-05-29 10:56:45.220299
2025-05-29 10:56:45,222 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:56:45,223 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:56:45,223 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:56:45.220299
2025-05-29 10:56:45,224 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:56:45,224 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:56:45,224 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:57:45,234 - root - INFO - 执行预约状态更新任务
2025-05-29 10:57:45,235 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:57:45.235563
2025-05-29 10:57:45,235 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=57, 秒=45
2025-05-29 10:57:45,236 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:57:45,236 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:57:45.235563 AND Reservation.end_datetime > 2025-05-29 10:57:45.235563
2025-05-29 10:57:45,237 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:57:45,237 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:57:45,238 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:57:45.235563
2025-05-29 10:57:45,238 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:57:45,239 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:57:45,239 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:58:45,247 - root - INFO - 执行预约状态更新任务
2025-05-29 10:58:45,248 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:58:45.248371
2025-05-29 10:58:45,248 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=58, 秒=45
2025-05-29 10:58:45,250 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:58:45,251 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:58:45.248371 AND Reservation.end_datetime > 2025-05-29 10:58:45.248371
2025-05-29 10:58:45,253 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:58:45,253 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:58:45,253 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:58:45.248371
2025-05-29 10:58:45,254 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:58:45,254 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:58:45,254 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:59:45,257 - root - INFO - 执行预约状态更新任务
2025-05-29 10:59:45,258 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:59:45.258160
2025-05-29 10:59:45,258 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=59, 秒=45
2025-05-29 10:59:45,259 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:59:45,260 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:59:45.258160 AND Reservation.end_datetime > 2025-05-29 10:59:45.258160
2025-05-29 10:59:45,261 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:59:45,262 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:59:45,262 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:59:45.258160
2025-05-29 10:59:45,263 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:59:45,264 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:59:45,264 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 11:00:45,273 - root - INFO - 执行预约状态更新任务
2025-05-29 11:00:45,273 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 11:00:45.273777
2025-05-29 11:00:45,274 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=11, 分=0, 秒=45
2025-05-29 11:00:45,275 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 11:00:45,275 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 11:00:45.273777 AND Reservation.end_datetime > 2025-05-29 11:00:45.273777
2025-05-29 11:00:45,276 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 11:00:45,276 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 11:00:45,276 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 11:00:45.273777
2025-05-29 11:00:45,277 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 11:00:45,277 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 11:00:45,278 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 11:01:45,283 - root - INFO - 执行预约状态更新任务
2025-05-29 11:01:45,283 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 11:01:45.283688
2025-05-29 11:01:45,284 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=11, 分=1, 秒=45
2025-05-29 11:01:45,285 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 11:01:45,285 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 11:01:45.283688 AND Reservation.end_datetime > 2025-05-29 11:01:45.283688
2025-05-29 11:01:45,286 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 11:01:45,286 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 11:01:45,286 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 11:01:45.283688
2025-05-29 11:01:45,287 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 11:01:45,287 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 11:01:45,287 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 11:02:45,290 - root - INFO - 执行预约状态更新任务
2025-05-29 11:02:45,291 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 11:02:45.291406
2025-05-29 11:02:45,291 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=11, 分=2, 秒=45
2025-05-29 11:02:45,292 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 11:02:45,293 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 11:02:45.291406 AND Reservation.end_datetime > 2025-05-29 11:02:45.291406
2025-05-29 11:02:45,294 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 11:02:45,294 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 11:02:45,294 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 11:02:45.291406
2025-05-29 11:02:45,295 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 11:02:45,295 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 11:02:45,295 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 11:03:45,301 - root - INFO - 执行预约状态更新任务
2025-05-29 11:03:45,301 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 11:03:45.301808
2025-05-29 11:03:45,302 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=11, 分=3, 秒=45
2025-05-29 11:03:45,303 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 11:03:45,303 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 11:03:45.301808 AND Reservation.end_datetime > 2025-05-29 11:03:45.301808
2025-05-29 11:03:45,304 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 11:03:45,304 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 11:03:45,304 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 11:03:45.301808
2025-05-29 11:03:45,305 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 11:03:45,305 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 11:03:45,306 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 11:04:45,314 - root - INFO - 执行预约状态更新任务
2025-05-29 11:04:45,315 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 11:04:45.315472
2025-05-29 11:04:45,315 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=11, 分=4, 秒=45
2025-05-29 11:04:45,316 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 11:04:45,317 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 11:04:45.315472 AND Reservation.end_datetime > 2025-05-29 11:04:45.315472
2025-05-29 11:04:45,317 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 11:04:45,317 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 11:04:45,318 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 11:04:45.315472
2025-05-29 11:04:45,318 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 11:04:45,319 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 11:04:45,319 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 11:05:45,332 - root - INFO - 执行预约状态更新任务
2025-05-29 11:05:45,332 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 11:05:45.332909
2025-05-29 11:05:45,333 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=11, 分=5, 秒=45
2025-05-29 11:05:45,334 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 11:05:45,334 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 11:05:45.332909 AND Reservation.end_datetime > 2025-05-29 11:05:45.332909
2025-05-29 11:05:45,335 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 11:05:45,335 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 11:05:45,335 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 11:05:45.332909
2025-05-29 11:05:45,336 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 11:05:45,336 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 11:05:45,336 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 11:06:01,233 - backend.routes.reservation - INFO - [API调用] 通过预约序号获取预约详情: 预约序号=RN-20250516-8875
2025-05-29 11:06:01,237 - backend.routes.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=222, 状态=confirmed, 预约序号=RN-20250516-8875
2025-05-29 11:06:01,237 - backend.routes.reservation - INFO - [API响应] 成功获取预约: ID=222, 状态=confirmed, 开始时间=2025-06-03 02:00:00, 结束时间=2025-06-03 09:00:00, 预约序号=RN-20250516-8875
2025-05-29 11:06:03,368 - backend.routes.reservation - INFO - [API调用] 通过预约序号获取预约详情: 预约序号=RN-20250516-8875
2025-05-29 11:06:03,372 - backend.routes.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=222, 状态=confirmed, 预约序号=RN-20250516-8875
2025-05-29 11:06:03,373 - backend.routes.reservation - INFO - [API响应] 成功获取预约: ID=222, 状态=confirmed, 开始时间=2025-06-03 02:00:00, 结束时间=2025-06-03 09:00:00, 预约序号=RN-20250516-8875
2025-05-29 11:06:45,343 - root - INFO - 执行预约状态更新任务
2025-05-29 11:06:45,343 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 11:06:45.343664
2025-05-29 11:06:45,344 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=11, 分=6, 秒=45
2025-05-29 11:06:45,345 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 11:06:45,345 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 11:06:45.343664 AND Reservation.end_datetime > 2025-05-29 11:06:45.343664
2025-05-29 11:06:45,346 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 11:06:45,346 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 11:06:45,346 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 11:06:45.343664
2025-05-29 11:06:45,347 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 11:06:45,347 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 11:06:45,347 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 11:07:45,350 - root - INFO - 执行预约状态更新任务
2025-05-29 11:07:45,351 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 11:07:45.351221
2025-05-29 11:07:45,351 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=11, 分=7, 秒=45
2025-05-29 11:07:45,352 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 11:07:45,352 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 11:07:45.351221 AND Reservation.end_datetime > 2025-05-29 11:07:45.351221
2025-05-29 11:07:45,353 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 11:07:45,353 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 11:07:45,353 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 11:07:45.351221
2025-05-29 11:07:45,354 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 11:07:45,354 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 11:07:45,355 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 11:08:45,359 - root - INFO - 执行预约状态更新任务
2025-05-29 11:08:45,359 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 11:08:45.359498
2025-05-29 11:08:45,359 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=11, 分=8, 秒=45
2025-05-29 11:08:45,360 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 11:08:45,361 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 11:08:45.359498 AND Reservation.end_datetime > 2025-05-29 11:08:45.359498
2025-05-29 11:08:45,362 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 11:08:45,362 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 11:08:45,362 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 11:08:45.359498
2025-05-29 11:08:45,363 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 11:08:45,364 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 11:08:45,364 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 11:09:45,370 - root - INFO - 执行预约状态更新任务
2025-05-29 11:09:45,370 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 11:09:45.370633
2025-05-29 11:09:45,371 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=11, 分=9, 秒=45
2025-05-29 11:09:45,372 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 11:09:45,372 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 11:09:45.370633 AND Reservation.end_datetime > 2025-05-29 11:09:45.370633
2025-05-29 11:09:45,373 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 11:09:45,374 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 11:09:45,374 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 11:09:45.370633
2025-05-29 11:09:45,375 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 11:09:45,375 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 11:09:45,376 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 11:10:45,391 - root - INFO - 执行预约状态更新任务
2025-05-29 11:10:45,392 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 11:10:45.392307
2025-05-29 11:10:45,392 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=11, 分=10, 秒=45
2025-05-29 11:10:45,393 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 11:10:45,393 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 11:10:45.392307 AND Reservation.end_datetime > 2025-05-29 11:10:45.392307
2025-05-29 11:10:45,394 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 11:10:45,394 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 11:10:45,395 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 11:10:45.392307
2025-05-29 11:10:45,395 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 11:10:45,396 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 11:10:45,396 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 11:11:45,412 - root - INFO - 执行预约状态更新任务
2025-05-29 11:11:45,412 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 11:11:45.412829
2025-05-29 11:11:45,413 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=11, 分=11, 秒=45
2025-05-29 11:11:45,414 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 11:11:45,414 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 11:11:45.412829 AND Reservation.end_datetime > 2025-05-29 11:11:45.412829
2025-05-29 11:11:45,415 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 11:11:45,415 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 11:11:45,415 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 11:11:45.412829
2025-05-29 11:11:45,416 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 11:11:45,416 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 11:11:45,416 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 11:12:45,431 - root - INFO - 执行预约状态更新任务
2025-05-29 11:12:45,432 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 11:12:45.432058
2025-05-29 11:12:45,432 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=11, 分=12, 秒=45
2025-05-29 11:12:45,433 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 11:12:45,434 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 11:12:45.432058 AND Reservation.end_datetime > 2025-05-29 11:12:45.432058
2025-05-29 11:12:45,434 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 11:12:45,435 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 11:12:45,435 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 11:12:45.432058
2025-05-29 11:12:45,436 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 11:12:45,436 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 11:12:45,436 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 11:13:45,450 - root - INFO - 执行预约状态更新任务
2025-05-29 11:13:45,451 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 11:13:45.451344
2025-05-29 11:13:45,451 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=11, 分=13, 秒=45
2025-05-29 11:13:45,452 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 11:13:45,453 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 11:13:45.451344 AND Reservation.end_datetime > 2025-05-29 11:13:45.451344
2025-05-29 11:13:45,453 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 11:13:45,454 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 11:13:45,454 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 11:13:45.451344
2025-05-29 11:13:45,455 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 11:13:45,455 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 11:13:45,455 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 11:14:45,456 - root - INFO - 执行预约状态更新任务
2025-05-29 11:14:45,457 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 11:14:45.457232
2025-05-29 11:14:45,457 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=11, 分=14, 秒=45
2025-05-29 11:14:45,458 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 11:14:45,459 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 11:14:45.457232 AND Reservation.end_datetime > 2025-05-29 11:14:45.457232
2025-05-29 11:14:45,460 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 11:14:45,460 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 11:14:45,460 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 11:14:45.457232
2025-05-29 11:14:45,461 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 11:14:45,461 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 11:14:45,462 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 15:58:57,985 - root - INFO - 设备预定系统启动中...
2025-05-29 15:58:58,327 - root - INFO - 应用程序将在 http://localhost:8000 启动
2025-05-29 15:58:58,327 - root - INFO - 局域网可通过 http://************:8000 访问
2025-05-29 15:58:58,445 - root - INFO - 设备预定系统启动中...
2025-05-29 15:58:58,878 - root - INFO - FastAPI应用初始化中...
2025-05-29 15:58:58,882 - backend.i18n - INFO - 国际化设置成功
2025-05-29 15:58:59,535 - root - INFO - 应用启动 / Application started
2025-05-29 15:58:59,543 - backend.database - INFO - 数据库初始化成功
2025-05-29 15:58:59,543 - root - INFO - 后台任务已启动
2025-05-29 15:58:59,544 - root - INFO - 执行预约状态更新任务
2025-05-29 15:58:59,544 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 15:58:59.544877
2025-05-29 15:58:59,545 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=15, 分=58, 秒=59
2025-05-29 15:58:59,562 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 15:58:59,562 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 15:58:59.544877 AND Reservation.end_datetime > 2025-05-29 15:58:59.544877
2025-05-29 15:58:59,565 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 15:58:59,566 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 15:58:59,566 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 15:58:59.544877
2025-05-29 15:58:59,568 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 15:58:59,568 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 15:58:59,568 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 15:59:35,837 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 15:59:39,544 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 15:59:40,890 - backend.routes.reservation - INFO - [API调用] 通过预约序号获取预约详情: 预约序号=RN-20250620-9315-5
2025-05-29 15:59:40,896 - backend.routes.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=232, 状态=confirmed, 预约序号=RN-20250620-9315-5
2025-05-29 15:59:40,897 - backend.routes.reservation - INFO - [API响应] 成功获取预约: ID=232, 状态=confirmed, 开始时间=2025-06-20 21:27:00, 结束时间=2025-06-20 22:27:00, 预约序号=RN-20250620-9315-5
2025-05-29 15:59:42,269 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 15:59:47,574 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 15:59:59,582 - root - INFO - 执行预约状态更新任务
2025-05-29 15:59:59,583 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 15:59:59.583747
2025-05-29 15:59:59,585 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=15, 分=59, 秒=59
2025-05-29 15:59:59,588 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 15:59:59,589 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 15:59:59.583747 AND Reservation.end_datetime > 2025-05-29 15:59:59.583747
2025-05-29 15:59:59,590 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 15:59:59,590 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 15:59:59,590 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 15:59:59.583747
2025-05-29 15:59:59,592 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 15:59:59,592 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 15:59:59,593 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 16:00:08,277 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 16:00:12,298 - backend.routes.reservation - INFO - [API调用] 通过预约序号获取预约详情: 预约序号=RN-20250516-8875
2025-05-29 16:00:12,302 - backend.routes.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=222, 状态=confirmed, 预约序号=RN-20250516-8875
2025-05-29 16:00:12,303 - backend.routes.reservation - INFO - [API响应] 成功获取预约: ID=222, 状态=confirmed, 开始时间=2025-06-03 02:00:00, 结束时间=2025-06-03 09:00:00, 预约序号=RN-20250516-8875
2025-05-29 16:00:59,602 - root - INFO - 执行预约状态更新任务
2025-05-29 16:00:59,603 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 16:00:59.603842
2025-05-29 16:00:59,605 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=16, 分=0, 秒=59
2025-05-29 16:00:59,608 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 16:00:59,608 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 16:00:59.603842 AND Reservation.end_datetime > 2025-05-29 16:00:59.603842
2025-05-29 16:00:59,609 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 16:00:59,609 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 16:00:59,609 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 16:00:59.603842
2025-05-29 16:00:59,610 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 16:00:59,610 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 16:00:59,611 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 16:01:59,611 - root - INFO - 执行预约状态更新任务
2025-05-29 16:01:59,612 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 16:01:59.612528
2025-05-29 16:01:59,612 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=16, 分=1, 秒=59
2025-05-29 16:01:59,614 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 16:01:59,614 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 16:01:59.612528 AND Reservation.end_datetime > 2025-05-29 16:01:59.612528
2025-05-29 16:01:59,615 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 16:01:59,615 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 16:01:59,615 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 16:01:59.612528
2025-05-29 16:01:59,616 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 16:01:59,617 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 16:01:59,617 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 16:02:59,626 - root - INFO - 执行预约状态更新任务
2025-05-29 16:02:59,626 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 16:02:59.626835
2025-05-29 16:02:59,628 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=16, 分=2, 秒=59
2025-05-29 16:02:59,629 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 16:02:59,629 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 16:02:59.626835 AND Reservation.end_datetime > 2025-05-29 16:02:59.626835
2025-05-29 16:02:59,630 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 16:02:59,631 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 16:02:59,631 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 16:02:59.626835
2025-05-29 16:02:59,632 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 16:02:59,633 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 16:02:59,633 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 16:03:59,645 - root - INFO - 执行预约状态更新任务
2025-05-29 16:03:59,645 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 16:03:59.645738
2025-05-29 16:03:59,646 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=16, 分=3, 秒=59
2025-05-29 16:03:59,647 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 16:03:59,647 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 16:03:59.645738 AND Reservation.end_datetime > 2025-05-29 16:03:59.645738
2025-05-29 16:03:59,648 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 16:03:59,648 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 16:03:59,648 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 16:03:59.645738
2025-05-29 16:03:59,649 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 16:03:59,649 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 16:03:59,650 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 16:04:59,664 - root - INFO - 执行预约状态更新任务
2025-05-29 16:04:59,665 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 16:04:59.665401
2025-05-29 16:04:59,665 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=16, 分=4, 秒=59
2025-05-29 16:04:59,666 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 16:04:59,667 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 16:04:59.665401 AND Reservation.end_datetime > 2025-05-29 16:04:59.665401
2025-05-29 16:04:59,668 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 16:04:59,668 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 16:04:59,668 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 16:04:59.665401
2025-05-29 16:04:59,669 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 16:04:59,669 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 16:04:59,669 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 16:05:59,684 - root - INFO - 执行预约状态更新任务
2025-05-29 16:05:59,685 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 16:05:59.685258
2025-05-29 16:05:59,685 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=16, 分=5, 秒=59
2025-05-29 16:05:59,686 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 16:05:59,686 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 16:05:59.685258 AND Reservation.end_datetime > 2025-05-29 16:05:59.685258
2025-05-29 16:05:59,687 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 16:05:59,688 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 16:05:59,688 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 16:05:59.685258
2025-05-29 16:05:59,688 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 16:05:59,689 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 16:05:59,689 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 16:06:59,699 - root - INFO - 执行预约状态更新任务
2025-05-29 16:06:59,700 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 16:06:59.700555
2025-05-29 16:06:59,700 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=16, 分=6, 秒=59
2025-05-29 16:06:59,702 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 16:06:59,702 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 16:06:59.700555 AND Reservation.end_datetime > 2025-05-29 16:06:59.700555
2025-05-29 16:06:59,703 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 16:06:59,703 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 16:06:59,703 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 16:06:59.700555
2025-05-29 16:06:59,704 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 16:06:59,704 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 16:06:59,705 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 16:07:59,717 - root - INFO - 执行预约状态更新任务
2025-05-29 16:07:59,718 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 16:07:59.718313
2025-05-29 16:07:59,718 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=16, 分=7, 秒=59
2025-05-29 16:07:59,719 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 16:07:59,720 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 16:07:59.718313 AND Reservation.end_datetime > 2025-05-29 16:07:59.718313
2025-05-29 16:07:59,721 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 16:07:59,721 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 16:07:59,721 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 16:07:59.718313
2025-05-29 16:07:59,722 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 16:07:59,722 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 16:07:59,723 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 16:08:59,727 - root - INFO - 执行预约状态更新任务
2025-05-29 16:08:59,728 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 16:08:59.728427
2025-05-29 16:08:59,728 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=16, 分=8, 秒=59
2025-05-29 16:08:59,729 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 16:08:59,729 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 16:08:59.728427 AND Reservation.end_datetime > 2025-05-29 16:08:59.728427
2025-05-29 16:08:59,730 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 16:08:59,730 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 16:08:59,731 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 16:08:59.728427
2025-05-29 16:08:59,731 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 16:08:59,732 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 16:08:59,732 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 16:09:10,887 - backend.routes.reservation - INFO - [API调用] 通过预约序号获取预约详情: 预约序号=RN-20250516-8875
2025-05-29 16:09:10,890 - backend.routes.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=222, 状态=confirmed, 预约序号=RN-20250516-8875
2025-05-29 16:09:10,891 - backend.routes.reservation - INFO - [API响应] 成功获取预约: ID=222, 状态=confirmed, 开始时间=2025-06-03 02:00:00, 结束时间=2025-06-03 09:00:00, 预约序号=RN-20250516-8875
2025-05-29 16:09:14,497 - backend.routes.reservation - INFO - [API调用] 通过预约序号获取预约详情: 预约序号=RN-20250516-8875
2025-05-29 16:09:14,500 - backend.routes.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=222, 状态=confirmed, 预约序号=RN-20250516-8875
2025-05-29 16:09:14,501 - backend.routes.reservation - INFO - [API响应] 成功获取预约: ID=222, 状态=confirmed, 开始时间=2025-06-03 02:00:00, 结束时间=2025-06-03 09:00:00, 预约序号=RN-20250516-8875
2025-05-29 16:09:26,655 - backend.routes.reservation - INFO - [API调用] 通过预约序号获取预约详情: 预约序号=RN-20250516-8875
2025-05-29 16:09:26,657 - backend.routes.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=222, 状态=confirmed, 预约序号=RN-20250516-8875
2025-05-29 16:09:26,658 - backend.routes.reservation - INFO - [API响应] 成功获取预约: ID=222, 状态=confirmed, 开始时间=2025-06-03 02:00:00, 结束时间=2025-06-03 09:00:00, 预约序号=RN-20250516-8875
2025-05-29 16:09:30,554 - backend.routes.reservation - INFO - [API调用] 通过预约序号获取预约详情: 预约序号=RN-20250516-8875
2025-05-29 16:09:30,558 - backend.routes.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=222, 状态=confirmed, 预约序号=RN-20250516-8875
2025-05-29 16:09:30,559 - backend.routes.reservation - INFO - [API响应] 成功获取预约: ID=222, 状态=confirmed, 开始时间=2025-06-03 02:00:00, 结束时间=2025-06-03 09:00:00, 预约序号=RN-20250516-8875
2025-05-29 16:09:31,956 - backend.routes.reservation - INFO - [API调用] 通过预约序号获取预约详情: 预约序号=RN-20250516-8875
2025-05-29 16:09:31,959 - backend.routes.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=222, 状态=confirmed, 预约序号=RN-20250516-8875
2025-05-29 16:09:31,960 - backend.routes.reservation - INFO - [API响应] 成功获取预约: ID=222, 状态=confirmed, 开始时间=2025-06-03 02:00:00, 结束时间=2025-06-03 09:00:00, 预约序号=RN-20250516-8875
2025-05-29 16:09:42,132 - backend.routes.reservation - INFO - [API调用] 通过预约序号获取预约详情: 预约序号=RN-20250516-8875
2025-05-29 16:09:42,135 - backend.routes.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=222, 状态=confirmed, 预约序号=RN-20250516-8875
2025-05-29 16:09:42,136 - backend.routes.reservation - INFO - [API响应] 成功获取预约: ID=222, 状态=confirmed, 开始时间=2025-06-03 02:00:00, 结束时间=2025-06-03 09:00:00, 预约序号=RN-20250516-8875
2025-05-29 16:09:46,890 - backend.routes.reservation - INFO - [API调用] 通过预约序号获取预约详情: 预约序号=RN-20250516-8875
2025-05-29 16:09:46,893 - backend.routes.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=222, 状态=confirmed, 预约序号=RN-20250516-8875
2025-05-29 16:09:46,894 - backend.routes.reservation - INFO - [API响应] 成功获取预约: ID=222, 状态=confirmed, 开始时间=2025-06-03 02:00:00, 结束时间=2025-06-03 09:00:00, 预约序号=RN-20250516-8875
2025-05-29 16:09:53,046 - backend.routes.reservation - INFO - [API调用] 通过预约序号获取预约详情: 预约序号=RN-20250516-8875
2025-05-29 16:09:53,049 - backend.routes.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=222, 状态=confirmed, 预约序号=RN-20250516-8875
2025-05-29 16:09:53,050 - backend.routes.reservation - INFO - [API响应] 成功获取预约: ID=222, 状态=confirmed, 开始时间=2025-06-03 02:00:00, 结束时间=2025-06-03 09:00:00, 预约序号=RN-20250516-8875
2025-05-29 16:09:55,028 - backend.routes.reservation - INFO - [API调用] 通过预约序号获取预约详情: 预约序号=RN-20250516-8875
2025-05-29 16:09:55,033 - backend.routes.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=222, 状态=confirmed, 预约序号=RN-20250516-8875
2025-05-29 16:09:55,034 - backend.routes.reservation - INFO - [API响应] 成功获取预约: ID=222, 状态=confirmed, 开始时间=2025-06-03 02:00:00, 结束时间=2025-06-03 09:00:00, 预约序号=RN-20250516-8875
2025-05-29 16:09:58,070 - backend.routes.reservation - INFO - [API调用] 通过预约序号获取预约详情: 预约序号=RN-20250516-8875
2025-05-29 16:09:58,073 - backend.routes.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=222, 状态=confirmed, 预约序号=RN-20250516-8875
2025-05-29 16:09:58,073 - backend.routes.reservation - INFO - [API响应] 成功获取预约: ID=222, 状态=confirmed, 开始时间=2025-06-03 02:00:00, 结束时间=2025-06-03 09:00:00, 预约序号=RN-20250516-8875
2025-05-29 16:09:59,736 - root - INFO - 执行预约状态更新任务
2025-05-29 16:09:59,736 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 16:09:59.736955
2025-05-29 16:09:59,737 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=16, 分=9, 秒=59
2025-05-29 16:09:59,738 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 16:09:59,738 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 16:09:59.736955 AND Reservation.end_datetime > 2025-05-29 16:09:59.736955
2025-05-29 16:09:59,739 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 16:09:59,739 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 16:09:59,739 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 16:09:59.736955
2025-05-29 16:09:59,740 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 16:09:59,740 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 16:09:59,740 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 16:10:00,894 - backend.routes.reservation - INFO - [API调用] 通过预约序号获取预约详情: 预约序号=RN-20250516-8875
2025-05-29 16:10:00,896 - backend.routes.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=222, 状态=confirmed, 预约序号=RN-20250516-8875
2025-05-29 16:10:00,897 - backend.routes.reservation - INFO - [API响应] 成功获取预约: ID=222, 状态=confirmed, 开始时间=2025-06-03 02:00:00, 结束时间=2025-06-03 09:00:00, 预约序号=RN-20250516-8875
2025-05-29 16:10:04,229 - backend.routes.reservation - INFO - [API调用] 通过预约序号获取预约详情: 预约序号=RN-20250516-8875
2025-05-29 16:10:04,232 - backend.routes.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=222, 状态=confirmed, 预约序号=RN-20250516-8875
2025-05-29 16:10:04,233 - backend.routes.reservation - INFO - [API响应] 成功获取预约: ID=222, 状态=confirmed, 开始时间=2025-06-03 02:00:00, 结束时间=2025-06-03 09:00:00, 预约序号=RN-20250516-8875
2025-05-29 16:10:08,813 - backend.routes.reservation - INFO - [API调用] 通过预约序号获取预约详情: 预约序号=RN-20250516-8875
2025-05-29 16:10:08,817 - backend.routes.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=222, 状态=confirmed, 预约序号=RN-20250516-8875
2025-05-29 16:10:08,818 - backend.routes.reservation - INFO - [API响应] 成功获取预约: ID=222, 状态=confirmed, 开始时间=2025-06-03 02:00:00, 结束时间=2025-06-03 09:00:00, 预约序号=RN-20250516-8875
2025-05-29 16:10:50,760 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 16:10:59,745 - root - INFO - 执行预约状态更新任务
2025-05-29 16:10:59,747 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 16:10:59.747533
2025-05-29 16:10:59,748 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=16, 分=10, 秒=59
2025-05-29 16:10:59,750 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 16:10:59,751 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 16:10:59.747533 AND Reservation.end_datetime > 2025-05-29 16:10:59.747533
2025-05-29 16:10:59,751 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 16:10:59,752 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 16:10:59,752 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 16:10:59.747533
2025-05-29 16:10:59,753 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 16:10:59,753 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 16:10:59,753 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 16:11:02,134 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 16:11:03,755 - backend.routes.reservation - INFO - [API调用] 通过预约序号获取预约详情: 预约序号=RN-20250516-8875
2025-05-29 16:11:03,758 - backend.routes.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=222, 状态=confirmed, 预约序号=RN-20250516-8875
2025-05-29 16:11:03,758 - backend.routes.reservation - INFO - [API响应] 成功获取预约: ID=222, 状态=confirmed, 开始时间=2025-06-03 02:00:00, 结束时间=2025-06-03 09:00:00, 预约序号=RN-20250516-8875
2025-05-29 16:11:08,549 - backend.routes.reservation - INFO - [API调用] 通过预约序号获取预约详情: 预约序号=RN-20250516-8875
2025-05-29 16:11:08,551 - backend.routes.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=222, 状态=confirmed, 预约序号=RN-20250516-8875
2025-05-29 16:11:08,552 - backend.routes.reservation - INFO - [API响应] 成功获取预约: ID=222, 状态=confirmed, 开始时间=2025-06-03 02:00:00, 结束时间=2025-06-03 09:00:00, 预约序号=RN-20250516-8875
2025-05-29 16:11:09,878 - backend.routes.reservation - INFO - [API调用] 通过预约序号获取预约详情: 预约序号=RN-20250516-8875
2025-05-29 16:11:09,882 - backend.routes.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=222, 状态=confirmed, 预约序号=RN-20250516-8875
2025-05-29 16:11:09,883 - backend.routes.reservation - INFO - [API响应] 成功获取预约: ID=222, 状态=confirmed, 开始时间=2025-06-03 02:00:00, 结束时间=2025-06-03 09:00:00, 预约序号=RN-20250516-8875
2025-05-29 16:11:18,089 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 16:11:22,691 - backend.routes.reservation - INFO - [API调用] 通过预约序号获取预约详情: 预约序号=RN-20250516-8875
2025-05-29 16:11:22,694 - backend.routes.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=222, 状态=confirmed, 预约序号=RN-20250516-8875
2025-05-29 16:11:22,694 - backend.routes.reservation - INFO - [API响应] 成功获取预约: ID=222, 状态=confirmed, 开始时间=2025-06-03 02:00:00, 结束时间=2025-06-03 09:00:00, 预约序号=RN-20250516-8875
2025-05-29 16:11:38,825 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 16:11:40,310 - backend.routes.reservation - INFO - [API调用] 通过预约序号获取预约详情: 预约序号=RN-20250618-9315-4
2025-05-29 16:11:40,314 - backend.routes.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=231, 状态=confirmed, 预约序号=RN-20250618-9315-4
2025-05-29 16:11:40,314 - backend.routes.reservation - INFO - [API响应] 成功获取预约: ID=231, 状态=confirmed, 开始时间=2025-06-18 21:27:00, 结束时间=2025-06-18 22:27:00, 预约序号=RN-20250618-9315-4
2025-05-29 16:11:49,316 - backend.routes.reservation - INFO - [API调用] 通过预约序号获取预约详情: 预约序号=RN-20250516-8875
2025-05-29 16:11:49,320 - backend.routes.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=222, 状态=confirmed, 预约序号=RN-20250516-8875
2025-05-29 16:11:49,321 - backend.routes.reservation - INFO - [API响应] 成功获取预约: ID=222, 状态=confirmed, 开始时间=2025-06-03 02:00:00, 结束时间=2025-06-03 09:00:00, 预约序号=RN-20250516-8875
2025-05-29 16:11:52,664 - backend.routes.reservation - INFO - [API调用] 通过预约序号获取预约详情: 预约序号=RN-20250516-8875
2025-05-29 16:11:52,667 - backend.routes.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=222, 状态=confirmed, 预约序号=RN-20250516-8875
2025-05-29 16:11:52,667 - backend.routes.reservation - INFO - [API响应] 成功获取预约: ID=222, 状态=confirmed, 开始时间=2025-06-03 02:00:00, 结束时间=2025-06-03 09:00:00, 预约序号=RN-20250516-8875
2025-05-29 16:11:52,762 - backend.routes.reservation - INFO - [API调用] 通过预约序号获取预约详情: 预约序号=RN-20250618-9315-4
2025-05-29 16:11:52,765 - backend.routes.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=231, 状态=confirmed, 预约序号=RN-20250618-9315-4
2025-05-29 16:11:52,765 - backend.routes.reservation - INFO - [API响应] 成功获取预约: ID=231, 状态=confirmed, 开始时间=2025-06-18 21:27:00, 结束时间=2025-06-18 22:27:00, 预约序号=RN-20250618-9315-4
2025-05-29 16:11:52,767 - backend.routes.reservation - INFO - [API调用] 通过预约序号获取预约详情: 预约序号=RN-20250516-8875
2025-05-29 16:11:52,770 - backend.routes.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=222, 状态=confirmed, 预约序号=RN-20250516-8875
2025-05-29 16:11:52,770 - backend.routes.reservation - INFO - [API响应] 成功获取预约: ID=222, 状态=confirmed, 开始时间=2025-06-03 02:00:00, 结束时间=2025-06-03 09:00:00, 预约序号=RN-20250516-8875
2025-05-29 16:11:59,759 - root - INFO - 执行预约状态更新任务
2025-05-29 16:11:59,760 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 16:11:59.760885
2025-05-29 16:11:59,762 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=16, 分=11, 秒=59
2025-05-29 16:11:59,763 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 16:11:59,764 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 16:11:59.760885 AND Reservation.end_datetime > 2025-05-29 16:11:59.760885
2025-05-29 16:11:59,765 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 16:11:59,765 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 16:11:59,766 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 16:11:59.760885
2025-05-29 16:11:59,767 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 16:11:59,767 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 16:11:59,768 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 16:12:59,775 - root - INFO - 执行预约状态更新任务
2025-05-29 16:12:59,776 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 16:12:59.776348
2025-05-29 16:12:59,777 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=16, 分=12, 秒=59
2025-05-29 16:12:59,778 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 16:12:59,778 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 16:12:59.776348 AND Reservation.end_datetime > 2025-05-29 16:12:59.776348
2025-05-29 16:12:59,779 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 16:12:59,780 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 16:12:59,780 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 16:12:59.776348
2025-05-29 16:12:59,781 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 16:12:59,782 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 16:12:59,782 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 17:11:23,396 - root - INFO - 设备预定系统启动中...
2025-05-29 17:11:23,718 - root - INFO - 应用程序将在 http://localhost:8000 启动
2025-05-29 17:11:23,718 - root - INFO - 局域网可通过 http://************:8000 访问
2025-05-29 17:11:23,834 - root - INFO - 设备预定系统启动中...
2025-05-29 17:11:24,235 - root - INFO - FastAPI应用初始化中...
2025-05-29 17:11:24,238 - backend.i18n - INFO - 国际化设置成功
2025-05-29 17:11:24,660 - root - INFO - 应用启动 / Application started
2025-05-29 17:11:24,662 - backend.database - INFO - 数据库初始化成功
2025-05-29 17:11:24,662 - root - INFO - 后台任务已启动
2025-05-29 17:11:24,662 - root - INFO - 执行预约状态更新任务
2025-05-29 17:11:24,663 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 17:11:24.663247
2025-05-29 17:11:24,663 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=17, 分=11, 秒=24
2025-05-29 17:11:24,679 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 17:11:24,680 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 17:11:24.663247 AND Reservation.end_datetime > 2025-05-29 17:11:24.663247
2025-05-29 17:11:24,682 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 17:11:24,682 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 17:11:24,682 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 17:11:24.663247
2025-05-29 17:11:24,683 - backend.utils.status_updater - INFO - 找到 1 个应该更新为'已过期'的预约
2025-05-29 17:11:24,684 - backend.utils.status_updater - INFO - 预约ID: 179, 开始时间: 2025-05-29 09:00:00, 结束时间: 2025-05-29 17:00:00, 当前状态: in_use
2025-05-29 17:11:24,684 - backend.utils.status_updater - INFO - 预约ID: 179, 结束时间与当前时间的差值(秒): -684.663247
2025-05-29 17:11:24,685 - backend.utils.status_updater - INFO - 执行SQL更新: 将ID在 [179] 中的预约状态更新为'已过期'
2025-05-29 17:11:24,687 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=1条
2025-05-29 17:11:24,690 - backend.utils.status_updater - INFO - 已更新预约状态: 0个更新为'使用中', 1个更新为'已过期'
2025-05-29 17:11:24,691 - backend.utils.status_updater - INFO - 更新后的预约状态:
2025-05-29 17:11:24,692 - backend.utils.status_updater - INFO - ID: 179, 预约序号: RN-20250515-0691, 状态: expired, 开始时间: 2025-05-29 09:00:00, 结束时间: 2025-05-29 17:00:00
2025-05-29 17:11:47,593 - backend.routes.reservation - INFO - [API调用] 通过预约序号获取预约详情: 预约序号=RN-20250516-8875
2025-05-29 17:11:47,631 - backend.routes.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=222, 状态=confirmed, 预约序号=RN-20250516-8875
2025-05-29 17:11:47,632 - backend.routes.reservation - INFO - [API响应] 成功获取预约: ID=222, 状态=confirmed, 开始时间=2025-06-03 02:00:00, 结束时间=2025-06-03 09:00:00, 预约序号=RN-20250516-8875
2025-05-29 17:11:48,967 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 17:11:54,090 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 17:12:14,937 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 17:12:17,807 - backend.routes.reservation - INFO - [API调用] 通过预约序号获取预约详情: 预约序号=RN-20250620-9315-5
2025-05-29 17:12:17,810 - backend.routes.reservation - INFO - [数据库查询] 通过预约序号找到预约: ID=232, 状态=confirmed, 预约序号=RN-20250620-9315-5
2025-05-29 17:12:17,810 - backend.routes.reservation - INFO - [API响应] 成功获取预约: ID=232, 状态=confirmed, 开始时间=2025-06-20 21:27:00, 结束时间=2025-06-20 22:27:00, 预约序号=RN-20250620-9315-5
2025-05-29 17:12:24,701 - root - INFO - 执行预约状态更新任务
2025-05-29 17:12:24,702 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 17:12:24.702331
2025-05-29 17:12:24,702 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=17, 分=12, 秒=24
2025-05-29 17:12:24,703 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 17:12:24,703 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 17:12:24.702331 AND Reservation.end_datetime > 2025-05-29 17:12:24.702331
2025-05-29 17:12:24,704 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 17:12:24,704 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 17:12:24,705 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 17:12:24.702331
2025-05-29 17:12:24,706 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 17:12:24,706 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 17:12:24,706 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 17:12:47,521 - backend.services.log_service - INFO - 操作日志已记录: 管理员 admin 登录成功
2025-05-29 17:12:47,678 - backend.services.log_service - INFO - 操作日志已记录: GET /api/statistics/dashboard - 200
2025-05-29 17:12:47,705 - backend.services.log_service - INFO - 操作日志已记录: GET /api/reservation - 307
2025-05-29 17:12:47,732 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 17:12:47,794 - backend.services.log_service - INFO - 操作日志已记录: GET /api/reservation/ - 200
2025-05-29 17:12:55,431 - backend.services.log_service - INFO - 操作日志已记录: GET /api/equipment - 307
2025-05-29 17:12:55,439 - backend.services.log_service - INFO - 操作日志已记录: GET /api/equipment-categories/all - 200
2025-05-29 17:12:55,465 - backend.services.log_service - INFO - 操作日志已记录: GET /api/equipment/ - 200
2025-05-29 17:13:04,180 - backend.services.log_service - INFO - 操作日志已记录: GET /api/reservation - 307
2025-05-29 17:13:04,184 - backend.services.log_service - INFO - 操作日志已记录: GET /api/reservation - 307
2025-05-29 17:13:04,207 - backend.routes.reservation - INFO - 排序参数: sort_by=id, sort_order=desc
2025-05-29 17:13:04,214 - backend.services.log_service - INFO - 操作日志已记录: GET /api/reservation/ - 200
2025-05-29 17:13:04,215 - backend.routes.reservation - INFO - 排序参数: sort_by=id, sort_order=desc
2025-05-29 17:13:04,224 - backend.services.log_service - INFO - 操作日志已记录: GET /api/reservation/ - 200
2025-05-29 17:13:10,563 - backend.services.log_service - INFO - 操作日志已记录: GET /api/announcements/all - 200
2025-05-29 17:13:13,785 - backend.services.log_service - INFO - 操作日志已记录: GET /api/admin/email/settings - 200
2025-05-29 17:13:16,471 - backend.services.log_service - INFO - 操作日志已记录: GET /api/admin/email/templates - 200
2025-05-29 17:13:17,364 - backend.services.log_service - INFO - 操作日志已记录: GET /api/admin/email/logs - 200
2025-05-29 17:13:19,201 - backend.services.log_service - INFO - 操作日志已记录: GET /api/db/tables - 200
2025-05-29 17:13:19,229 - backend.services.log_service - INFO - 操作日志已记录: GET /api/db/table/admin/columns - 200
2025-05-29 17:13:19,254 - backend.services.log_service - INFO - 操作日志已记录: GET /api/db/table/admin/rows - 200
2025-05-29 17:13:21,736 - backend.services.log_service - INFO - 操作日志已记录: GET /api/system/logs - 307
2025-05-29 17:13:21,777 - backend.services.log_service - INFO - 操作日志已记录: GET /api/system/logs/ - 200
2025-05-29 17:13:24,268 - backend.services.log_service - INFO - 操作日志已记录: GET /api/admin - 307
2025-05-29 17:13:24,290 - backend.services.log_service - INFO - 操作日志已记录: GET /api/admin/ - 200
2025-05-29 17:13:24,716 - root - INFO - 执行预约状态更新任务
2025-05-29 17:13:24,717 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 17:13:24.717870
2025-05-29 17:13:24,718 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=17, 分=13, 秒=24
2025-05-29 17:13:24,721 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 17:13:24,721 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 17:13:24.717870 AND Reservation.end_datetime > 2025-05-29 17:13:24.717870
2025-05-29 17:13:24,723 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 17:13:24,723 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 17:13:24,724 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 17:13:24.717870
2025-05-29 17:13:24,725 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 17:13:24,725 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 17:13:24,725 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 17:14:24,735 - root - INFO - 执行预约状态更新任务
2025-05-29 17:14:24,736 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 17:14:24.736329
2025-05-29 17:14:24,736 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=17, 分=14, 秒=24
2025-05-29 17:14:24,737 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 17:14:24,737 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 17:14:24.736329 AND Reservation.end_datetime > 2025-05-29 17:14:24.736329
2025-05-29 17:14:24,738 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 17:14:24,738 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 17:14:24,739 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 17:14:24.736329
2025-05-29 17:14:24,739 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 17:14:24,740 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 17:14:24,740 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 17:14:28,220 - backend.services.log_service - INFO - 操作日志已记录: GET /api/announcements/ - 200
2025-05-29 17:14:28,235 - backend.services.log_service - INFO - 操作日志已记录: GET /api/equipment-categories - 307
2025-05-29 17:14:28,259 - backend.services.log_service - INFO - 操作日志已记录: GET /api/equipment-categories/ - 200
2025-05-29 17:14:32,328 - backend.services.log_service - INFO - 操作日志已记录: GET /api/equipment - 307
2025-05-29 17:14:32,332 - backend.services.log_service - INFO - 操作日志已记录: GET /api/equipment-categories/all - 200
2025-05-29 17:14:32,355 - backend.services.log_service - INFO - 操作日志已记录: GET /api/equipment/ - 200
2025-05-29 17:14:36,749 - backend.services.log_service - INFO - 操作日志已记录: GET /api/statistics/dashboard - 200
2025-05-29 17:14:36,768 - backend.services.log_service - INFO - 操作日志已记录: GET /api/reservation - 307
2025-05-29 17:14:36,780 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 17:14:36,849 - backend.services.log_service - INFO - 操作日志已记录: GET /api/reservation/ - 200
2025-05-29 17:14:40,455 - backend.services.log_service - INFO - 操作日志已记录: GET /api/announcements/all - 200
2025-05-29 17:14:58,156 - backend.services.log_service - INFO - 操作日志已记录: GET /api/admin/email/settings - 200
2025-05-29 17:15:17,468 - backend.services.log_service - INFO - 操作日志已记录: GET /api/db/tables - 200
2025-05-29 17:15:17,488 - backend.services.log_service - INFO - 操作日志已记录: GET /api/db/table/admin/columns - 200
2025-05-29 17:15:17,506 - backend.services.log_service - INFO - 操作日志已记录: GET /api/db/table/admin/rows - 200
2025-05-29 17:15:18,866 - backend.services.log_service - INFO - 操作日志已记录: GET /api/system/logs - 307
2025-05-29 17:15:18,885 - backend.services.log_service - INFO - 操作日志已记录: GET /api/system/logs/ - 200
2025-05-29 17:15:24,756 - root - INFO - 执行预约状态更新任务
2025-05-29 17:15:24,756 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 17:15:24.756955
2025-05-29 17:15:24,757 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=17, 分=15, 秒=24
2025-05-29 17:15:24,758 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 17:15:24,758 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 17:15:24.756955 AND Reservation.end_datetime > 2025-05-29 17:15:24.756955
2025-05-29 17:15:24,759 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 17:15:24,760 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 17:15:24,760 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 17:15:24.756955
2025-05-29 17:15:24,761 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 17:15:24,761 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 17:15:24,761 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 17:15:29,396 - backend.services.log_service - INFO - 操作日志已记录: GET /api/admin - 307
2025-05-29 17:15:29,409 - backend.services.log_service - INFO - 操作日志已记录: GET /api/admin/ - 200
2025-05-29 17:15:36,110 - backend.services.log_service - INFO - 操作日志已记录: GET /api/reservation - 307
2025-05-29 17:15:36,115 - backend.services.log_service - INFO - 操作日志已记录: GET /api/reservation - 307
2025-05-29 17:15:36,149 - backend.routes.reservation - INFO - 排序参数: sort_by=id, sort_order=desc
2025-05-29 17:15:36,154 - backend.routes.reservation - INFO - 排序参数: sort_by=id, sort_order=desc
2025-05-29 17:15:36,161 - backend.services.log_service - INFO - 操作日志已记录: GET /api/reservation/ - 200
2025-05-29 17:15:36,163 - backend.services.log_service - INFO - 操作日志已记录: GET /api/reservation/ - 200
